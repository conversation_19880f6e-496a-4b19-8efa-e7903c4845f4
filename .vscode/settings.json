{"WillLuke.nextjs.addTypesOnSave": true, "WillLuke.nextjs.hasPrompted": true, "[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "editor.defaultFormatter": "rvest.vs-code-prettier-eslint", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "editor.formatOnType": false, "files.autoSave": "onFocusChange", "vs-code-prettier-eslint.prettierLast": false}
import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      // Your existing font family setup
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },

      // The detailed typography styles for your blog content
      typography: ({ theme }) => ({
        DEFAULT: {
          css: {
            // --- GENERAL STYLES (Light Mode) ---
            '--tw-prose-body': theme('colors.stone[800]'),
            '--tw-prose-headings': theme('colors.stone[900]'),
            '--tw-prose-links': theme('colors.teal[600]'),
            '--tw-prose-bold': theme('colors.stone[900]'),
            '--tw-prose-bullets': theme('colors.stone[400]'),

            // --- DARK MODE STYLES ---
            '--tw-prose-invert-body': theme('colors.stone[300]'),
            '--tw-prose-invert-headings': theme('colors.stone[100]'),
            '--tw-prose-invert-links': theme('colors.teal[400]'),
            '--tw-prose-invert-bold': theme('colors.stone[100]'),
            '--tw-prose-invert-bullets': theme('colors.stone[600]'),
            '--tw-prose-invert-pre-bg': 'hsl(220, 15%, 15%)',
            '--tw-prose-invert-pre-code': theme('colors.stone[300]'),

            // --- BASE STYLING ---
            p: {
              marginTop: theme('spacing.2'),
              marginBottom: theme('spacing.6'),
              lineHeight: '1.7',
            },

            // --- HEADINGS ---
            h1: {
              fontWeight: '800',
              fontSize: theme('fontSize.4xl'),
              marginBottom: theme('spacing.6'),
            },
            h2: {
              fontWeight: '700',
              fontSize: theme('fontSize.3xl'),
              marginTop: theme('spacing.16'),
              marginBottom: theme('spacing.4'),
            },
            h3: {
              fontWeight: '600',
              fontSize: theme('fontSize.2xl'),
              marginTop: theme('spacing.12'),
              marginBottom: theme('spacing.3'),
            },

            // --- CODE BLOCKS ---
            pre: {
              padding: theme('spacing.6'),
              borderRadius: theme('borderRadius.xl'),
              border: `1px solid ${theme('colors.stone.700')}`,
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
            },
          },
        },
      }),
    },
  },
  darkMode: "class",
  plugins: [heroui(), require('@tailwindcss/typography')],
};

module.exports = config;
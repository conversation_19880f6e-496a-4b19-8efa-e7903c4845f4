import { Role } from "@prisma/client";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string?;
      roles: Role[];
      emailVerified: Date?;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string?;
    roles: Role[];
    emailVerified: Date?;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    email: string;
    name: string?;
    roles: Role[];
    emailVerified: Date?;
  }
}

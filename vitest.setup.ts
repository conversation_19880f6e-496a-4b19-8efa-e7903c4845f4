import "@testing-library/jest-dom";
import { afterEach, beforeEach } from "node:test";

import { vi } from "vitest";

// Mock Next.js router
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  })),
  useSearchParams: vi.fn(() => new URLSearchParams()),
  usePathname: vi.fn(() => "/"),
  notFound: vi.fn(),
  redirect: vi.fn(),
}));

// Mock NextAuth
vi.mock("next-auth/react", () => ({
  useSession: vi.fn(() => ({
    data: null,
    status: "unauthenticated",
  })),
  signIn: vi.fn(),
  signOut: vi.fn(),
  SessionProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock NextAuth server functions
vi.mock("next-auth", () => ({
  default: vi.fn(),
  getServerSession: vi.fn(),
}));

vi.mock("next-auth/next", () => ({
  NextAuth: vi.fn(),
}));

vi.mock("next-auth/providers/credentials", () => ({
  default: vi.fn(() => ({
    id: "credentials",
    name: "credentials",
    type: "credentials",
    credentials: {},
    authorize: vi.fn(),
  })),
}));

vi.mock("next-auth/jwt", () => ({
  getToken: vi.fn(),
}));

// Mock Prisma
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      create: vi.fn(),
      findMany: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    app: {
      findUnique: vi.fn(),
      create: vi.fn(),
      findMany: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    advertisement: {
      findUnique: vi.fn(),
      create: vi.fn(),
      findMany: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    adImpression: {
      create: vi.fn(),
      findMany: vi.fn(),
    },
    $queryRaw: vi.fn(),
  },
}));

// Mock bcryptjs
vi.mock("bcryptjs", () => ({
  default: {
    hash: vi.fn(),
    compare: vi.fn(),
  },
  hash: vi.fn(),
  compare: vi.fn(),
}));

// Mock file system operations
vi.mock("fs/promises", () => ({
  writeFile: vi.fn(),
  readFile: vi.fn(),
  unlink: vi.fn(),
  mkdir: vi.fn(),
}));

// Mock path module
vi.mock("path", async () => {
  const actual = await vi.importActual("path");

  return {
    ...actual,
    join: vi.fn((...args: string[]) => args.join("/")),
  };
});

// Mock nanoid
vi.mock("nanoid", () => ({
  nanoid: vi.fn(() => "mock-id-12345"),
}));

// Mock middleware functions
vi.mock("@/lib/middleware", () => ({
  withMiddleware: vi.fn(
    (..._middlewares) =>
      (handler: any) =>
        handler,
  ),
  withRateLimit: vi.fn(() => (handler: any) => handler),
  withAuth: vi.fn(() => (handler: any) => handler),
  withCors: vi.fn(() => (handler: any) => handler),
  withSecurityHeaders: vi.fn(() => (handler: any) => handler),
  withApiSecurity: vi.fn((handler: any) => handler),
  withAuthAndSecurity: vi.fn((handler: any) => handler),
  withUploadSecurity: vi.fn((handler: any) => handler),
  withServeSecurity: vi.fn((handler: any) => handler),
}));

// Mock validation functions
vi.mock("@/lib/validation", async () => {
  const actual = await vi.importActual("@/lib/validation");

  return {
    ...actual,
    withRateLimit: vi.fn(() => (handler: any) => handler),
    rateLimitConfigs: {
      auth: { windowMs: 15 * 60 * 1000, maxRequests: 5 },
      api: { windowMs: 60 * 1000, maxRequests: 100 },
      upload: { windowMs: 60 * 1000, maxRequests: 10 },
      serve: { windowMs: 60 * 1000, maxRequests: 1000 },
    },
  };
});

// Mock crypto for Node.js environment
Object.defineProperty(global, "crypto", {
  value: {
    randomUUID: vi.fn(() => "mock-uuid-12345"),
    getRandomValues: vi.fn((arr: any) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }

      return arr;
    }),
  },
});

// Mock fetch globally
global.fetch = vi.fn();

// Mock FormData
global.FormData = class FormData {
  private data: Map<string, any> = new Map();

  append(key: string, value: any) {
    this.data.set(key, value);
  }

  get(key: string) {
    return this.data.get(key);
  }

  has(key: string) {
    return this.data.has(key);
  }
} as any;

// Mock File
global.File = class File {
  name: string;
  size: number;
  type: string;

  constructor(chunks: any[], filename: string, options: any = {}) {
    this.name = filename;
    this.size = options.size || 0;
    this.type = options.type || "";
  }

  arrayBuffer() {
    return Promise.resolve(new ArrayBuffer(0));
  }
} as any;

// Mock URL
global.URL = class URL {
  href: string;
  origin: string;
  protocol: string;
  hostname: string;
  port: string;
  pathname: string;
  search: string;
  hash: string;

  constructor(url: string, _base?: string) {
    this.href = url;
    this.origin = "http://localhost:3000";
    this.protocol = "http:";
    this.hostname = "localhost";
    this.port = "3000";
    this.pathname = "/";
    this.search = "";
    this.hash = "";
  }

  toString() {
    return this.href;
  }
} as any;

// Setup and cleanup
beforeEach(() => {
  vi.clearAllMocks();
});

afterEach(() => {
  vi.restoreAllMocks();
});

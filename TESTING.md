# AI Advertisement Management Platform - Testing Guide

## Overview

This document provides comprehensive testing instructions for the AI Advertisement Management Platform. The project uses **Vitest** for fast, modern testing with excellent TypeScript support and comprehensive coverage.

## Quick Start

```bash
# Install dependencies
bun install

# Run all tests
bun run test:run

# Run tests in watch mode
bun run test:watch

# Generate coverage report
bun run test:coverage
```

## Test Architecture

### Testing Framework: Vitest

We chose Vitest over Jest for several advantages:
- ⚡ **Faster execution** - Native ESM support and optimized for speed
- 🔧 **Better TypeScript support** - No additional configuration needed
- 🎯 **Modern features** - Built-in coverage, watch mode, and UI
- 🔄 **Hot reload** - Instant test re-runs during development
- 📊 **Built-in coverage** - No additional setup required

### Test Structure

```
__tests__/
├── api/                    # API endpoint tests
│   ├── auth/
│   │   └── register.test.ts
│   ├── apps.test.ts
│   ├── ads.test.ts
│   ├── serve-ad.test.ts
│   └── impressions.test.ts
├── lib/                    # Utility function tests
│   ├── auth.test.ts
│   ├── validation.test.ts
│   ├── middleware.test.ts
│   └── logger.test.ts
├── components/             # React component tests
│   └── [component].test.tsx
└── integration/            # End-to-end flow tests
    └── user-flow.test.ts
```

## Test Categories

### 1. Unit Tests

**API Routes** (`__tests__/api/`)
- Authentication endpoints (`/api/auth/*`)
- App management (`/api/apps`)
- Advertisement management (`/api/ads`)
- Ad serving (`/api/serve-ad`)
- Impression tracking (`/api/impressions`)
- Analytics (`/api/analytics/*`)
- File upload (`/api/upload`)

**Utility Functions** (`__tests__/lib/`)
- Authentication utilities (`lib/auth.ts`)
- Input validation (`lib/validation.ts`)
- Middleware functions (`lib/middleware.ts`)
- Logging utilities (`lib/logger.ts`)

### 2. Integration Tests

**User Flows** (`__tests__/integration/`)
- Complete user registration and login
- Model provider app registration
- Advertiser campaign creation
- Ad serving and impression tracking
- Analytics data generation

### 3. Component Tests

**React Components** (`__tests__/components/`)
- File upload component
- Analytics charts
- Dashboard components
- Authentication forms

## Running Tests

### Basic Commands

```bash
# Run all tests once
bun run test:run

# Run tests in watch mode (recommended for development)
bun run test:watch

# Run tests with interactive UI
bun run test:ui

# Generate and view coverage report
bun run test:coverage
```

### Advanced Commands

```bash
# Run specific test file
bun run test:run __tests__/api/auth/register.test.ts

# Run tests matching a pattern
bun run test:run --grep "registration"

# Run tests for specific directory
bun run test:run __tests__/api/

# Run tests with verbose output
bun run test:run --reporter=verbose

# Run tests and update snapshots
bun run test:run --update-snapshots
```

## Test Configuration

### Vitest Configuration (`vitest.config.ts`)

```typescript
export default defineConfig({
  test: {
    environment: 'jsdom',           // DOM testing environment
    setupFiles: ['./vitest.setup.ts'], // Global test setup
    globals: true,                  // Global test functions
    coverage: {
      provider: 'v8',              // Fast native coverage
      reporter: ['text', 'html'],   // Coverage report formats
      thresholds: {                 // Minimum coverage requirements
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    }
  }
})
```

### Test Setup (`vitest.setup.ts`)

The setup file includes comprehensive mocks for:
- **Next.js** router and navigation
- **NextAuth.js** authentication
- **Prisma** database operations
- **File system** operations
- **External APIs** and services

## Writing Tests

### Test Structure Template

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'

describe('Feature Name', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Success Cases', () => {
    it('should handle valid input', async () => {
      // Arrange
      const mockData = { /* test data */ }
      vi.mocked(dependency).mockResolvedValue(mockData)

      // Act
      const result = await functionUnderTest(input)

      // Assert
      expect(result).toEqual(expectedOutput)
      expect(dependency).toHaveBeenCalledWith(expectedArgs)
    })
  })

  describe('Error Cases', () => {
    it('should handle invalid input', async () => {
      // Test error scenarios
      vi.mocked(dependency).mockRejectedValue(new Error('Test error'))
      
      await expect(functionUnderTest(invalidInput))
        .rejects.toThrow('Expected error message')
    })
  })
})
```

### API Route Testing

```typescript
import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/example/route'

it('should handle POST request', async () => {
  const request = new NextRequest('http://localhost:3000/api/example', {
    method: 'POST',
    body: JSON.stringify({ data: 'test' }),
    headers: { 'Content-Type': 'application/json' }
  })

  const response = await POST(request)
  const data = await response.json()

  expect(response.status).toBe(200)
  expect(data).toEqual(expectedResponse)
})
```

### Component Testing

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { Component } from '@/components/Component'

it('should render component correctly', () => {
  render(<Component prop="value" />)
  
  expect(screen.getByText('Expected Text')).toBeInTheDocument()
  
  fireEvent.click(screen.getByRole('button'))
  expect(mockFunction).toHaveBeenCalled()
})
```

## Mocking Strategies

### Database Operations

```typescript
vi.mock('@/lib/db', () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      create: vi.fn(),
      // ... other methods
    }
  }
}))
```

### External APIs

```typescript
global.fetch = vi.fn()

// In test
vi.mocked(fetch).mockResolvedValue({
  ok: true,
  json: () => Promise.resolve({ data: 'test' })
})
```

### File Operations

```typescript
vi.mock('fs/promises', () => ({
  writeFile: vi.fn(),
  readFile: vi.fn(),
}))
```

## Coverage Requirements

### Current Coverage Targets

- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Viewing Coverage

```bash
# Generate coverage report
bun run test:coverage

# Open HTML coverage report
open coverage/index.html
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   Error: Cannot resolve module
   ```
   - Check that imports are properly mocked in `vitest.setup.ts`
   - Verify path aliases are configured correctly

2. **Database Connection Errors**
   ```bash
   Error: Database connection failed
   ```
   - Tests use mocked Prisma - no real database needed
   - Ensure Prisma mocks are properly configured

3. **Environment Variable Issues**
   ```bash
   Error: Environment variable not found
   ```
   - Tests use mock values defined in setup
   - Add missing env vars to test setup if needed

4. **Async/Await Issues**
   ```bash
   Error: Promise not resolved
   ```
   - Ensure all async operations are properly awaited
   - Check that mocks return promises when expected

### Debug Mode

```bash
# Run tests with debug output
bun run test:run --reporter=verbose

# Run single test with debugging
bun run test:run --grep "specific test" --reporter=verbose
```

## Continuous Integration

### GitHub Actions

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: oven-sh/setup-bun@v1
      - run: bun install
      - run: bun run test:coverage
      - run: bun run type-check
      - run: bun run lint
```

## Best Practices

1. **Test Organization**
   - Group related tests with `describe` blocks
   - Use descriptive test names
   - Follow AAA pattern (Arrange, Act, Assert)

2. **Mocking**
   - Mock external dependencies
   - Use realistic test data
   - Clear mocks between tests

3. **Assertions**
   - Test both success and error cases
   - Verify function calls and arguments
   - Check response structure and content

4. **Performance**
   - Keep tests fast and focused
   - Avoid unnecessary async operations
   - Use parallel execution when possible

## Contributing

When adding new features:

1. **Write tests first** (TDD approach)
2. **Maintain coverage** above minimum thresholds
3. **Test edge cases** and error scenarios
4. **Update documentation** when adding new test patterns

For questions or issues with testing, please check the troubleshooting section or open an issue.

"use client";

import {
  Navbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Navbar<PERSON><PERSON><PERSON>,
  NavbarMenu,
  NavbarMenuToggle,
  NavbarBrand,
  NavbarItem,
  NavbarMenuItem,
} from "@heroui/navbar";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@heroui/dropdown";
import { Avatar } from "@heroui/avatar";
import { link as linkStyles } from "@heroui/theme";
import NextLink from "next/link";
import clsx from "clsx";
import { useSession, signOut } from "next-auth/react";

import { siteConfig } from "@/config/site";
import { ThemeSwitch } from "@/components/theme-switch";
import { Logo } from "@/components/icons";

export const Navbar = () => {
  const { data: session, status } = useSession();
  const handleSignOut = () => signOut({ callbackUrl: "/" });

  return (
    <HeroUINavbar
      className="top-0 z-50 backdrop-blur bg-default-0/80 shadow-sm"
      maxWidth="xl"
      position="sticky"
    >
      {/* Brand */}
      <NavbarContent className="flex-none" justify="start">
        <NavbarBrand as="li" className="max-w-fit">
          <NextLink className="flex items-center gap-2 group" href="/">
            <Logo className="w-8 h-8 transition-transform group-hover:scale-105 text-primary" />
            <span className="text-lg font-extrabold text-foreground transition-colors group-hover:text-primary">
              Mindify AI
            </span>
          </NextLink>
        </NavbarBrand>
      </NavbarContent>

      {/* Centered Nav Items */}
      <NavbarContent className="hidden lg:flex flex-1 gap-6" justify="center">
        {siteConfig.navItems.map((item) => (
          <NavbarItem key={item.href}>
            <NextLink
              className={clsx(
                linkStyles({ color: "foreground" }),
                "relative py-2 before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5 before:bg-primary before:transition-all hover:before:w-full data-[active=true]:before:w-full data-[active=true]:font-semibold",
              )}
              href={item.href}
            >
              {item.label}
            </NextLink>
          </NavbarItem>
        ))}
      </NavbarContent>

      {/* Actions */}
      <NavbarContent
        className="flex-none hidden md:flex items-center gap-4"
        justify="end"
      >
        <ThemeSwitch />

        {status === "loading" ? (
          <NavbarItem>
            <div className="w-8 h-8 rounded-full animate-pulse bg-default-200" />
          </NavbarItem>
        ) : session ? (
          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <Avatar
                as="button"
                className="transition-transform hover:scale-110"
                color="primary"
                name={session.user.email?.charAt(0).toUpperCase()}
                size="md"
              />
            </DropdownTrigger>
            <DropdownMenu aria-label="Profile" className="w-56" variant="flat">
              {/* Profile header as disabled item */}
              <DropdownItem
                key="profile-header"
                isDisabled
                className="pointer-events-none p-0"
              >
                <div className="px-4 py-3 border-b border-default-200">
                  <p className="text-sm text-default-500">Signed in as</p>
                  <p className="font-medium truncate text-foreground">
                    {session.user.email}
                  </p>
                </div>
              </DropdownItem>

              {Array.isArray(session?.user?.roles) &&
              session.user.roles?.includes("MODEL_PROVIDER") ? (
                <DropdownItem
                  key="model-dashboard"
                  as={NextLink}
                  href="/dashboard/model"
                >
                  Model Dashboard
                </DropdownItem>
              ) : null}
              {Array.isArray(session?.user?.roles) &&
              session.user.roles?.includes("ADVERTISER") ? (
                <DropdownItem
                  key="advertiser-dashboard"
                  as={NextLink}
                  href="/dashboard/advertiser"
                >
                  Advertiser Dashboard
                </DropdownItem>
              ) : null}
              <DropdownItem key="settings" as={NextLink} href="/settings">
                Settings
              </DropdownItem>
              <DropdownItem key="logout" color="danger" onClick={handleSignOut}>
                Log Out
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        ) : (
          <NavbarItem>
            <div className="flex items-center gap-2">
              <Button as={NextLink} href="/login" size="sm" variant="flat">
                Sign In
              </Button>
              <Button as={NextLink} color="primary" href="/register" size="sm">
                Sign Up
              </Button>
            </div>
          </NavbarItem>
        )}
      </NavbarContent>

      {/* Mobile Toggle */}
      <NavbarContent className="md:hidden" justify="end">
        <ThemeSwitch />
        <NavbarMenuToggle />
      </NavbarContent>

      {/* Mobile Menu */}
      <NavbarMenu className="p-4 bg-default-0">
        {siteConfig.navItems.map((item) => (
          <NavbarMenuItem key={item.href}>
            <Link as={NextLink} href={item.href} size="lg">
              {item.label}
            </Link>
          </NavbarMenuItem>
        ))}

        {session ? (
          <>
            <NavbarMenuItem>
              <div className="mt-4 border-t border-default-200 pt-4">
                <p className="mb-2 text-sm text-default-500">Signed in as</p>
                <p className="font-medium truncate mb-4 text-foreground">
                  {session.user.email}
                </p>
              </div>
            </NavbarMenuItem>

            {session.user.roles.includes("MODEL_PROVIDER") && (
              <NavbarMenuItem
                key="mobile-model-dashboard"
                as={NextLink}
                href="/dashboard/model"
              >
                Model Dashboard
              </NavbarMenuItem>
            )}
            {session.user.roles.includes("ADVERTISER") && (
              <NavbarMenuItem
                key="mobile-advertiser-dashboard"
                as={NextLink}
                href="/dashboard/advertiser"
              >
                Advertiser Dashboard
              </NavbarMenuItem>
            )}
            <NavbarMenuItem
              key="mobile-settings"
              as={NextLink}
              href="/settings"
            >
              Settings
            </NavbarMenuItem>
            <NavbarMenuItem key="mobile-logout" onClick={handleSignOut}>
              Log Out
            </NavbarMenuItem>
          </>
        ) : (
          <>
            <NavbarMenuItem key="mobile-login" as={NextLink} href="/login">
              Sign In
            </NavbarMenuItem>
            <NavbarMenuItem
              key="mobile-register"
              as={NextLink}
              href="/register"
            >
              Sign Up
            </NavbarMenuItem>
          </>
        )}
      </NavbarMenu>
    </HeroUINavbar>
  );
};

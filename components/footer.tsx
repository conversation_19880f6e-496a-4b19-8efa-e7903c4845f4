import { Link } from "@heroui/link";
import NextLink from "next/link";
import { Twitter, GithubIcon, Linkedin } from "lucide-react";

import { Logo } from "@/components/icons";
import { siteConfig } from "@/config/site";

export const Footer = () => {
  const year = new Date().getFullYear();

  return (
    <footer className="bg-default-0 border-t border-default-200 py-12">
      <div className="max-w-screen-xl mx-auto px-4 grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Brand & Description */}
        <div className="flex flex-col items-start space-y-2">
          <div className="flex items-center gap-2">
            <Logo className="w-8 h-8 text-primary" />
            <span className="font-extrabold text-xl text-foreground">
              {siteConfig.name}
            </span>
          </div>
          {siteConfig.description && (
            <p className="text-sm text-default-500 max-w-xs">
              {siteConfig.description}
            </p>
          )}
        </div>

        {/* Navigation Links */}
        <nav aria-label="Footer Navigation">
          <ul className="flex flex-wrap justify-start md:justify-center gap-4 text-sm text-default-600">
            {siteConfig.navItems.map((item) => (
              <li key={item.href}>
                <Link as={NextLink} href={item.href} size="sm">
                  {item.label}
                </Link>
              </li>
            ))}
            <li>
              <Link as={NextLink} href="/terms" size="sm">
                Terms
              </Link>
            </li>
            <li>
              <Link as={NextLink} href="/privacy" size="sm">
                Privacy
              </Link>
            </li>
            <li>
              <Link as={NextLink} href="/contact" size="sm">
                Contact
              </Link>
            </li>
          </ul>
        </nav>

        {/* Social & Copyright */}
        <div className="flex flex-col items-start md:items-end space-y-4">
          <div className="flex gap-4">
            <a
              aria-label="Twitter"
              className="hover:text-primary"
              href={siteConfig.links.twitter}
            >
              <Twitter size={20} />
            </a>
            <a
              aria-label="GitHub"
              className="hover:text-primary"
              href={siteConfig.links.github}
            >
              <GithubIcon size={20} />
            </a>
            <a
              aria-label="LinkedIn"
              className="hover:text-primary"
              href={siteConfig.links.github}
            >
              <Linkedin size={20} />
            </a>
          </div>
          <p className="text-sm text-default-400">
            © {year} {siteConfig.name}. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

"use client";

import { Card, CardBody, CardHeader } from "@heroui/card";
import { Progress } from "@heroui/progress";
import { Chip } from "@heroui/chip";

interface TopPerformerItem {
  id: string;
  name: string;
  impressions: number;
  clicks: number;
  revenue?: number;
  spend?: number;
  ctr?: string;
}

interface TopPerformersProps {
  items: TopPerformerItem[];
  title: string;
  type: "apps" | "campaigns";
}

export default function TopPerformers({
  items,
  title,
  type,
}: TopPerformersProps) {
  const maxValue = Math.max(
    ...items.map((item) =>
      type === "apps" ? item.revenue || 0 : item.spend || 0,
    ),
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <h3 className="text-lg font-semibold">{title}</h3>
      </CardHeader>
      <CardBody>
        {items.length === 0 ? (
          <div className="text-center py-8 text-default-500">
            No data available
          </div>
        ) : (
          <div className="space-y-4">
            {items.map((item, index) => {
              const primaryValue =
                type === "apps" ? item.revenue || 0 : item.spend || 0;
              const progressValue =
                maxValue > 0 ? (primaryValue / maxValue) * 100 : 0;
              const ctr =
                item.impressions > 0
                  ? ((item.clicks / item.impressions) * 100).toFixed(2)
                  : "0.00";

              return (
                <div key={item.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/20 text-primary text-xs font-semibold">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{item.name}</p>
                        <div className="flex items-center gap-2 text-xs text-default-500">
                          <span>
                            {item.impressions.toLocaleString()} impressions
                          </span>
                          <span>•</span>
                          <span>{item.clicks.toLocaleString()} clicks</span>
                          <span>•</span>
                          <span>{ctr}% CTR</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-sm">
                        {type === "apps"
                          ? `$${(item.revenue || 0).toFixed(2)}`
                          : `$${(item.spend || 0).toFixed(2)}`}
                      </div>
                      <Chip
                        color={
                          parseFloat(ctr) >= 3
                            ? "success"
                            : parseFloat(ctr) >= 1.5
                              ? "warning"
                              : "default"
                        }
                        size="sm"
                        variant="flat"
                      >
                        {ctr}% CTR
                      </Chip>
                    </div>
                  </div>
                  <Progress
                    className="h-2"
                    color={type === "apps" ? "primary" : "secondary"}
                    value={progressValue}
                  />
                </div>
              );
            })}
          </div>
        )}
      </CardBody>
    </Card>
  );
}

"use client";

import { <PERSON>, Card<PERSON><PERSON>, CardHeader } from "@heroui/card";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ComposedChart,
  Line,
} from "recharts";

interface PerformanceData {
  month: string;
  impressions: number;
  clicks: number;
  spend: number;
}

interface PerformanceChartProps {
  data: PerformanceData[];
  title?: string;
}

export default function PerformanceChart({
  data,
  title = "Campaign Performance",
}: PerformanceChartProps) {
  const formatCurrency = (value: number) => `$${value.toFixed(2)}`;
  const formatNumber = (value: number) => value.toLocaleString();

  // Calculate CTR for each month
  const dataWithCTR = data.map((item) => ({
    ...item,
    ctr: item.impressions > 0 ? (item.clicks / item.impressions) * 100 : 0,
  }));

  return (
    <Card className="w-full">
      <CardHeader>
        <h3 className="text-lg font-semibold">{title}</h3>
      </CardHeader>
      <CardBody>
        <div className="h-80">
          <ResponsiveContainer height="100%" width="100%">
            <ComposedChart
              data={dataWithCTR}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid className="opacity-30" strokeDasharray="3 3" />
              <XAxis
                className="text-xs"
                dataKey="month"
                tick={{ fontSize: 12 }}
              />
              <YAxis
                className="text-xs"
                orientation="left"
                tick={{ fontSize: 12 }}
                tickFormatter={formatNumber}
                yAxisId="metrics"
              />
              <YAxis
                className="text-xs"
                orientation="right"
                tick={{ fontSize: 12 }}
                tickFormatter={formatCurrency}
                yAxisId="spend"
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "hsl(var(--background))",
                  border: "1px solid hsl(var(--border))",
                  borderRadius: "8px",
                  fontSize: "12px",
                }}
                formatter={(value: number, name: string) => {
                  if (name === "spend") return [formatCurrency(value), "Spend"];
                  if (name === "ctr") return [`${value.toFixed(2)}%`, "CTR"];

                  return [
                    formatNumber(value),
                    name === "impressions" ? "Impressions" : "Clicks",
                  ];
                }}
              />
              <Bar
                dataKey="impressions"
                fill="hsl(var(--primary))"
                opacity={0.8}
                radius={[2, 2, 0, 0]}
                yAxisId="metrics"
              />
              <Bar
                dataKey="clicks"
                fill="hsl(var(--success))"
                opacity={0.8}
                radius={[2, 2, 0, 0]}
                yAxisId="metrics"
              />
              <Line
                activeDot={{
                  r: 6,
                  stroke: "hsl(var(--danger))",
                  strokeWidth: 2,
                }}
                dataKey="spend"
                dot={{ fill: "hsl(var(--danger))", strokeWidth: 2, r: 4 }}
                stroke="hsl(var(--danger))"
                strokeWidth={3}
                type="monotone"
                yAxisId="spend"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        <div className="flex justify-center gap-6 mt-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary rounded-sm" />
            <span>Impressions</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-success rounded-sm" />
            <span>Clicks</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-danger rounded-full" />
            <span>Spend</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

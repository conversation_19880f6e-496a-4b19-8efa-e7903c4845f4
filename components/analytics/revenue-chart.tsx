"use client";

import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

interface RevenueData {
  month: string;
  impressions: number;
  clicks: number;
  revenue: number;
}

interface RevenueChartProps {
  data: RevenueData[];
  title?: string;
}

export default function RevenueChart({
  data,
  title = "Revenue Analytics",
}: RevenueChartProps) {
  const formatCurrency = (value: number) => `$${value.toFixed(2)}`;
  const formatNumber = (value: number) => value.toLocaleString();

  return (
    <Card className="w-full">
      <CardHeader>
        <h3 className="text-lg font-semibold">{title}</h3>
      </CardHeader>
      <CardBody>
        <div className="h-80">
          <ResponsiveContainer height="100%" width="100%">
            <LineChart
              data={data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid className="opacity-30" strokeDasharray="3 3" />
              <XAxis
                className="text-xs"
                dataKey="month"
                tick={{ fontSize: 12 }}
              />
              <YAxis
                className="text-xs"
                orientation="left"
                tick={{ fontSize: 12 }}
                tickFormatter={formatCurrency}
                yAxisId="revenue"
              />
              <YAxis
                className="text-xs"
                orientation="right"
                tick={{ fontSize: 12 }}
                tickFormatter={formatNumber}
                yAxisId="metrics"
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "hsl(var(--background))",
                  border: "1px solid hsl(var(--border))",
                  borderRadius: "8px",
                  fontSize: "12px",
                }}
                formatter={(value: number, name: string) => {
                  if (name === "revenue")
                    return [formatCurrency(value), "Revenue"];

                  return [
                    formatNumber(value),
                    name === "impressions" ? "Impressions" : "Clicks",
                  ];
                }}
              />
              <Line
                activeDot={{
                  r: 6,
                  stroke: "hsl(var(--primary))",
                  strokeWidth: 2,
                }}
                dataKey="revenue"
                dot={{ fill: "hsl(var(--primary))", strokeWidth: 2, r: 4 }}
                stroke="hsl(var(--primary))"
                strokeWidth={3}
                type="monotone"
                yAxisId="revenue"
              />
              <Line
                dataKey="impressions"
                dot={{ fill: "hsl(var(--success))", strokeWidth: 2, r: 3 }}
                stroke="hsl(var(--success))"
                strokeDasharray="5 5"
                strokeWidth={2}
                type="monotone"
                yAxisId="metrics"
              />
              <Line
                dataKey="clicks"
                dot={{ fill: "hsl(var(--warning))", strokeWidth: 2, r: 3 }}
                stroke="hsl(var(--warning))"
                strokeDasharray="5 5"
                strokeWidth={2}
                type="monotone"
                yAxisId="metrics"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="flex justify-center gap-6 mt-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary rounded-full" />
            <span>Revenue</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-1 bg-success rounded-full" />
            <span>Impressions</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-1 bg-warning rounded-full" />
            <span>Clicks</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

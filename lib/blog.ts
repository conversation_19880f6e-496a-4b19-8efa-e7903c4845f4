// lib/blog.ts
import fs from "fs";
import path from "path";

import matter from "gray-matter";
import { cache } from "react";

// Define the path to your content directory
const postsDir = path.join(process.cwd(), "content", "blog");

// Utility to get all post slugs for generateStaticParams
export function getPostSlugs(): string[] {
  try {
    return fs
      .readdirSync(postsDir)
      .filter((file) => path.extname(file) === ".mdx")
      .map((file) => file.replace(/\.mdx$/, ""));
  } catch (error) {
    console.error("Error reading post slugs:", error);

    return [];
  }
}

// Get a single post's data and content by its slug
// 'cache' deduplicates requests for the same slug within a render pass
export const getPostBySlug = cache(async (slug: string) => {
  const filePath = path.join(postsDir, `${slug}.mdx`);

  if (!fs.existsSync(filePath)) {
    return null; // Return null instead of throwing, let the page handle notFound
  }

  const fileContent = fs.readFileSync(filePath, "utf8");
  const { data, content } = matter(fileContent);

  // Type-safe frontmatter
  const frontmatter = {
    title: data.title || "Untitled Post",
    description: data.description || "",
    date: data.date
      ? new Date(data.date).toISOString()
      : new Date().toISOString(),
    author: data.author || "Anonymous",
    image: data.image || null,
  };

  return {
    frontmatter,
    content,
    slug,
  };
});

// Utility to get metadata for all posts, sorted by date
export async function getAllPosts() {
  const slugs = getPostSlugs();
  const posts = await Promise.all(slugs.map((slug) => getPostBySlug(slug)));

  // Filter out any null posts and sort by date descending
  return posts
    .filter((post) => post !== null)
    .sort(
      (a, b) =>
        new Date(b!.frontmatter.date).getTime() -
        new Date(a!.frontmatter.date).getTime(),
    );
}

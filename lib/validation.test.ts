import { describe, it, expect, vi, beforeEach } from "vitest";

import {
  validateData,
  sanitizeString,
  sanitizeEmail,
  checkRateLimit,
  registerSchema,
  appSchema,
  advertisementSchema,
  rateLimitConfigs,
} from "@/lib/validation";

describe("validation utilities", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("validateData", () => {
    it("should validate correct data", () => {
      const validData = {
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      };

      const result = validateData(registerSchema, validData);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it("should return error for invalid data", () => {
      const invalidData = {
        email: "invalid-email",
        password: "123",
        roles: [],
      };

      const result = validateData(registerSchema, invalidData);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeDefined();
      }
    });
  });

  describe("registerSchema", () => {
    it("should validate valid registration data", () => {
      const validData = {
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER", "ADVERTISER"],
      };

      const result = validateData(registerSchema, validData);

      expect(result.success).toBe(true);
    });

    it("should reject invalid email", () => {
      const invalidData = {
        email: "invalid-email",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      };

      const result = validateData(registerSchema, invalidData);

      expect(result.success).toBe(false);
    });

    it("should reject short password", () => {
      const invalidData = {
        email: "<EMAIL>",
        password: "123",
        roles: ["MODEL_PROVIDER"],
      };

      const result = validateData(registerSchema, invalidData);

      expect(result.success).toBe(false);
    });

    it("should accept empty roles (optional during registration)", () => {
      const validData = {
        email: "<EMAIL>",
        password: "password123",
        roles: [],
      };

      const result = validateData(registerSchema, validData);

      expect(result.success).toBe(true);
    });

    it("should accept missing roles field (defaults to empty array)", () => {
      const validData = {
        email: "<EMAIL>",
        password: "password123",
      };

      const result = validateData(registerSchema, validData);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.roles).toEqual([]);
      }
    });
  });

  describe("appSchema", () => {
    it("should validate valid app data", () => {
      const validData = {
        name: "Test App",
        description: "Test description",
      };

      const result = validateData(appSchema, validData);

      expect(result.success).toBe(true);
    });

    it("should reject invalid URL", () => {
      const invalidData = {
        name: "Test App",
        description: "Test description",
        // Adding an invalid URL field that shouldn't be there
        invalidField: "invalid-url",
      };

      const result = validateData(appSchema, invalidData);

      expect(result.success).toBe(true); // Should pass as extra fields are ignored
    });

    it("should reject empty name", () => {
      const invalidData = {
        name: "",
        description: "Test description",
      };

      const result = validateData(appSchema, invalidData);

      expect(result.success).toBe(false);
    });
  });

  describe("advertisementSchema", () => {
    it("should validate valid advertisement data", () => {
      const validData = {
        name: "Test Campaign",
        description: "Test description",
        productUrl: "https://example.com",
        targetTopics: ["AI", "productivity"],
        budget: 100,
        bidType: "CPC",
        bidAmount: 0.5,
      };

      const result = validateData(advertisementSchema, validData);

      expect(result.success).toBe(true);
    });

    it("should reject invalid bid type", () => {
      const invalidData = {
        name: "Test Campaign",
        description: "Test description",
        productUrl: "https://example.com",
        targetTopics: ["AI"],
        budget: 100,
        bidType: "INVALID",
        bidAmount: 0.5,
      };

      const result = validateData(advertisementSchema, invalidData);

      expect(result.success).toBe(false);
    });

    it("should reject negative budget", () => {
      const invalidData = {
        name: "Test Campaign",
        description: "Test description",
        productUrl: "https://example.com",
        targetTopics: ["AI"],
        budget: -10,
        bidType: "CPC",
        bidAmount: 0.5,
      };

      const result = validateData(advertisementSchema, invalidData);

      expect(result.success).toBe(false);
    });
  });

  describe("sanitization functions", () => {
    describe("sanitizeString", () => {
      it("should trim whitespace and remove dangerous characters", () => {
        const input = '  <script>alert("xss")</script>  ';
        const result = sanitizeString(input);

        expect(result).toBe('scriptalert("xss")/script');
      });

      it("should handle normal strings", () => {
        const input = "  Normal string  ";
        const result = sanitizeString(input);

        expect(result).toBe("Normal string");
      });
    });

    describe("sanitizeEmail", () => {
      it("should convert to lowercase and trim", () => {
        const input = "  <EMAIL>  ";
        const result = sanitizeEmail(input);

        expect(result).toBe("<EMAIL>");
      });
    });
  });

  describe("rate limiting", () => {
    describe("checkRateLimit", () => {
      it("should allow requests within limit", () => {
        const config = { windowMs: 60000, maxRequests: 5 };
        const key = "test-key";

        const result = checkRateLimit(key, config);

        expect(result.allowed).toBe(true);
        expect(result.remaining).toBe(4);
        expect(result.resetTime).toBeGreaterThan(Date.now());
      });

      it("should block requests exceeding limit", () => {
        const config = { windowMs: 60000, maxRequests: 2 };
        const key = "test-key-2";

        // Make requests up to the limit
        checkRateLimit(key, config);
        checkRateLimit(key, config);

        // This should be blocked
        const result = checkRateLimit(key, config);

        expect(result.allowed).toBe(false);
        expect(result.remaining).toBe(0);
      });

      it("should reset after time window", () => {
        const config = { windowMs: 1, maxRequests: 1 }; // 1ms window
        const key = "test-key-3";

        // Make a request
        checkRateLimit(key, config);

        // Wait for window to expire
        return new Promise((resolve) => {
          setTimeout(() => {
            const result = checkRateLimit(key, config);

            expect(result.allowed).toBe(true);
            expect(result.remaining).toBe(0);
            resolve(undefined);
          }, 2);
        });
      });
    });

    describe("rateLimitConfigs", () => {
      it("should have auth config", () => {
        expect(rateLimitConfigs.auth).toEqual({
          windowMs: 15 * 60 * 1000,
          maxRequests: 5,
        });
      });

      it("should have api config", () => {
        expect(rateLimitConfigs.api).toEqual({
          windowMs: 60 * 1000,
          maxRequests: 100,
        });
      });

      it("should have upload config", () => {
        expect(rateLimitConfigs.upload).toEqual({
          windowMs: 60 * 1000,
          maxRequests: 10,
        });
      });

      it("should have serve config", () => {
        expect(rateLimitConfigs.serve).toEqual({
          windowMs: 60 * 1000,
          maxRequests: 1000,
        });
      });
    });
  });
});

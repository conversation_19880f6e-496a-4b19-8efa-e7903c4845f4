import { Role } from "@prisma/client";
import { Session } from "next-auth";

/**
 * Determines the appropriate redirect path based on user's authentication status and roles
 */
export function getAuthRedirectPath(session: Session | null): string {
  // If no session, redirect to login
  if (!session || !session.user) {
    return "/login";
  }

  // If user has no roles assigned, redirect to setup
  if (!session.user.roles || session.user.roles.length === 0) {
    return "/setup";
  }

  // If user has only one role, redirect directly to that dashboard
  if (session.user.roles.length === 1) {
    if (session.user.roles.includes("MODEL_PROVIDER")) {
      return "/dashboard/model";
    } else if (session.user.roles.includes("ADVERTISER")) {
      return "/dashboard/advertiser";
    } else {
      // Unknown role, redirect to setup
      return "/setup";
    }
  }

  // If user has multiple roles, redirect to role selection dashboard
  return "/dashboard/role-selection";
}

/**
 * Checks if user has access to a specific dashboard
 */
export function hasRoleAccess(
  session: Session | null,
  requiredRole: string,
): boolean {
  if (!session || !session.user || !session.user.roles) {
    return false;
  }

  return session.user.roles.includes(requiredRole as Role);
}

/**
 * Gets the appropriate dashboard path for a user after role assignment
 */
export function getDashboardPathAfterRoleAssignment(
  selectedRoles: string[],
): string {
  if (selectedRoles.length === 0) {
    return "/setup";
  }

  if (selectedRoles.length === 1) {
    if (selectedRoles.includes("MODEL_PROVIDER")) {
      return "/dashboard/model";
    } else if (selectedRoles.includes("ADVERTISER")) {
      return "/dashboard/advertiser";
    } else {
      return "/setup";
    }
  }

  // Multiple roles - go to role selection dashboard
  return "/dashboard/role-selection";
}

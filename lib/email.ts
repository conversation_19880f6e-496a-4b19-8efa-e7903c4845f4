import crypto from "crypto";

import { Resend } from "resend";
import nodemailer from "nodemailer";

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Gmail transporter as fallback
const gmailTransporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.GMAIL_USER,
    pass: process.env.GMAIL_PASS,
  },
});

export interface EmailVerificationData {
  email: string;
  token: string;
  name?: string;
}

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  text: string;
  from?: string;
}

export interface EmailResult {
  success: boolean;
  error?: string;
  provider?: "resend" | "gmail";
}

export function generateVerificationToken(): string {
  return crypto.randomBytes(32).toString("hex");
}

export function getVerificationExpiry(): Date {
  // Token expires in 24 hours
  return new Date(Date.now() + 24 * 60 * 60 * 1000);
}

export function getVerificationUrl(token: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  return `${baseUrl}/verify-email?token=${token}`;
}

/**
 * Generic email sending function that handles both Resend and Gmail fallback
 */
export async function sendEmail(emailData: EmailData): Promise<EmailResult> {
  try {
    const fromEmail =
      emailData.from || process.env.EMAIL_FROM || "<EMAIL>";

    // Try Resend first
    if (
      process.env.RESEND_API_KEY &&
      process.env.RESEND_API_KEY !== "your_resend_api_key_here"
    ) {
      const result = await resend.emails.send({
        from: fromEmail,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      });

      if (!result.error) {
        console.log(`Email sent to ${emailData.to} via Resend`);

        return { success: true, provider: "resend" };
      }
      console.error("Resend failed:", result.error);
      console.log("Trying Gmail fallback...");
    }

    // Fallback to Gmail
    if (
      process.env.GMAIL_USER &&
      process.env.GMAIL_PASS &&
      process.env.GMAIL_USER !== "<EMAIL>" &&
      process.env.GMAIL_PASS !== "your_gmail_app_password_here"
    ) {
      await gmailTransporter.sendMail({
        from: fromEmail,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      });

      console.log(`Email sent to ${emailData.to} via Gmail`);

      return { success: true, provider: "gmail" };
    }

    throw new Error("No email service configured");
  } catch (error) {
    console.error("Failed to send email:", error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send email",
    };
  }
}

export function getEmailVerificationTemplate(data: EmailVerificationData): {
  subject: string;
  html: string;
  text: string;
} {
  const verificationUrl = getVerificationUrl(data.token);
  const name = data.name || "there";

  const subject = "Verify your email address - Mindify AiD Platform";

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Email Verification</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to Mindify AiD Platform!</h1>
          <p>Please verify your email address to get started</p>
        </div>
        <div class="content">
          <h2>Hi ${name},</h2>
          <p>Thank you for registering with Mindify AiD Platform! To complete your registration and start using our platform, please verify your email address by clicking the button below:</p>
          
          <div style="text-align: center;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </div>
          
          <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f0f0f0; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
          
          <div class="warning">
            <strong>Important:</strong> This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to register again.
          </div>
          
          <p>If you didn't create an account with us, please ignore this email.</p>
          
          <p>Best regards,<br>The Mindify AiD Platform Team</p>
        </div>
        <div class="footer">
          <p>© 2025 Mindify AiD Platform. All rights reserved.</p>
          <p>This is an automated email. Please do not reply to this message.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Welcome to Mindify AiD Platform!
    
    Hi ${name},
    
    Thank you for registering with Mindify AiD Platform! To complete your registration and start using our platform, please verify your email address by visiting this link:
    
    ${verificationUrl}
    
    Important: This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to register again.
    
    If you didn't create an account with us, please ignore this email.
    
    Best regards,
    The Mindify AiD Platform Team
    
    © 2025 Mindify AiD Platform. All rights reserved.
    This is an automated email. Please do not reply to this message.
  `;

  return { subject, html, text };
}

export async function sendVerificationEmail(
  data: EmailVerificationData,
): Promise<{ success: boolean; error?: string }> {
  const { subject, html, text } = getEmailVerificationTemplate(data);

  const result = await sendEmail({
    to: data.email,
    subject,
    html,
    text,
  });

  return {
    success: result.success,
    error: result.error,
  };
}

export function getWelcomeEmailTemplate(name?: string): {
  subject: string;
  html: string;
  text: string;
} {
  const displayName = name || "there";
  const subject = "Welcome to Mindify AiD Platform!";

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Mindify AiD Platform</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Welcome to Mindify AiD Platform!</h1>
          <p>Your email has been verified successfully</p>
        </div>
        <div class="content">
          <h2>Hi ${displayName},</h2>
          <p>Congratulations! Your email address has been verified and your account is now active.</p>

          <p>You can now:</p>
          <ul>
            <li>Access your dashboard</li>
            <li>Register AI applications (Model Providers)</li>
            <li>Create advertising campaigns (Advertisers)</li>
            <li>Start monetizing your AI applications</li>
          </ul>

          <div style="text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard" class="button">Go to Dashboard</a>
          </div>

          <p>If you have any questions or need help getting started, feel free to reach out to our support team.</p>

          <p>Best regards,<br>The Mindify AiD Platform Team</p>
        </div>
        <div class="footer">
          <p>© 2025 Mindify AiD Platform. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Welcome to Mindify AiD Platform!

    Hi ${displayName},

    Congratulations! Your email address has been verified and your account is now active.

    You can now:
    - Access your dashboard
    - Register AI applications (Model Providers)
    - Create advertising campaigns (Advertisers)
    - Start monetizing your AI applications

    Visit your dashboard: ${process.env.NEXT_PUBLIC_APP_URL}/dashboard

    If you have any questions or need help getting started, feel free to reach out to our support team.

    Best regards,
    The Mindify AiD Platform Team

    © 2025 Mindify AiD Platform. All rights reserved.
  `;

  return { subject, html, text };
}

export async function sendWelcomeEmail(
  email: string,
  name?: string,
): Promise<{ success: boolean; error?: string }> {
  const { subject, html, text } = getWelcomeEmailTemplate(name);

  const result = await sendEmail({
    to: email,
    subject,
    html,
    text,
  });

  return {
    success: result.success,
    error: result.error,
  };
}

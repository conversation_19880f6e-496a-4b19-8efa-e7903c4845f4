import { describe, it, expect, vi, beforeEach } from "vitest";
import bcrypt from "bcryptjs";

import { hashPassword, verifyPassword, authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";

vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
    account: {
      create: vi.fn(),
    },
  },
}));

vi.mock("bcryptjs", () => ({
  default: {
    hash: vi.fn(),
    compare: vi.fn(),
  },
  hash: vi.fn(),
  compare: vi.fn(),
}));

describe("auth utilities", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("hashPassword", () => {
    it("should hash password with bcrypt", async () => {
      const password = "testpassword123";
      const hashedPassword = "hashed-password";

      vi.mocked(bcrypt.hash).mockResolvedValue(hashedPassword as any);

      const result = await hashPassword(password);

      expect(bcrypt.hash).toHaveBeenCalledWith(password, 12);
      expect(result).toBe(hashedPassword);
    });

    it("should handle bcrypt errors", async () => {
      const password = "testpassword123";
      const error = new Error("Bcrypt error");

      vi.mocked(bcrypt.hash).mockRejectedValue(error);

      await expect(hashPassword(password)).rejects.toThrow("Bcrypt error");
    });
  });

  describe("verifyPassword", () => {
    it("should verify correct password", async () => {
      const password = "testpassword123";
      const hashedPassword = "hashed-password";

      vi.mocked(bcrypt.compare).mockResolvedValue(true as any);

      const result = await verifyPassword(password, hashedPassword);

      expect(bcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
      expect(result).toBe(true);
    });

    it("should reject incorrect password", async () => {
      const password = "wrongpassword";
      const hashedPassword = "hashed-password";

      vi.mocked(bcrypt.compare).mockResolvedValue(false as any);

      const result = await verifyPassword(password, hashedPassword);

      expect(bcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
      expect(result).toBe(false);
    });

    it("should handle bcrypt errors", async () => {
      const password = "testpassword123";
      const hashedPassword = "hashed-password";
      const error = new Error("Bcrypt error");

      vi.mocked(bcrypt.compare).mockRejectedValue(error);

      await expect(verifyPassword(password, hashedPassword)).rejects.toThrow(
        "Bcrypt error",
      );
    });
  });

  describe("JWT callback", () => {
    it("should add roles and emailVerified to token", async () => {
      const token = { sub: "user-123" };
      const verificationDate = new Date();
      const user = {
        roles: ["MODEL_PROVIDER"],
        emailVerified: verificationDate,
      };

      const result = await authOptions.callbacks?.jwt?.({
        token,
        user,
      } as any);

      expect(result).toEqual({
        sub: "user-123",
        roles: ["MODEL_PROVIDER"],
        emailVerified: verificationDate,
      });
    });

    it("should preserve existing token when no user", async () => {
      const token = { sub: "user-123", roles: ["ADVERTISER"] };

      const result = await authOptions.callbacks?.jwt?.({ token } as any);

      expect(result).toEqual(token);
    });
  });

  describe("session callback", () => {
    it("should add user info including emailVerified to session", async () => {
      const session = {
        user: { email: "<EMAIL>" },
      };
      const verificationDate = new Date();
      const token = {
        sub: "user-123",
        roles: ["MODEL_PROVIDER"],
        emailVerified: verificationDate,
      };

      const result = await authOptions.callbacks?.session?.({
        session,
        token,
      } as any);

      expect(result).toEqual({
        user: {
          email: "<EMAIL>",
          id: "user-123",
          roles: ["MODEL_PROVIDER"],
          emailVerified: verificationDate,
        },
      });
    });

    it("should handle missing token", async () => {
      const session = {
        user: { email: "<EMAIL>" },
      };

      const result = await authOptions.callbacks?.session?.({
        session,
      } as any);

      expect(result).toEqual(session);
    });
  });

  describe("OAuth Events", () => {
    it("should not assign roles for new OAuth users (role selection flow)", async () => {
      const mockUser = {
        id: "user-123",
        name: "Test User",
        email: "<EMAIL>",
        roles: [],
        emailVerified: null,
      };

      (prisma.user.update as any).mockResolvedValue({});

      await authOptions.events?.createUser?.({ user: mockUser });

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: {
          roles: [], // No roles assigned - user will select during onboarding
          emailVerified: expect.any(Date),
        },
      });
    });
  });

  describe("OAuth Callbacks", () => {
    const mockUser = {
      id: "user-123",
      name: "Test User",
      email: "<EMAIL>",
      roles: [],
      emailVerified: null,
    };

    it("should allow Google sign-ins", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        id: "user-123",
        email: "<EMAIL>",
        roles: [],
        emailVerified: null,
      } as any);

      const result = await authOptions.callbacks?.signIn?.({
        user: mockUser,
        account: {
          provider: "google",
          providerAccountId: "123",
          type: "oauth",
        },
        profile: {},
      });

      expect(result).toBe(true);
    });

    it("should allow credentials sign-ins", async () => {
      const result = await authOptions.callbacks?.signIn?.({
        user: mockUser,
        account: {
          provider: "credentials",
          type: "credentials",
          providerAccountId: "123",
        },
        profile: {},
      });

      expect(result).toBe(true);
    });

    it("should reject unknown providers", async () => {
      const result = await authOptions.callbacks?.signIn?.({
        user: mockUser,
        account: {
          provider: "unknown",
          type: "credentials",
          providerAccountId: "123",
        },
        profile: {},
      });

      expect(result).toBe(false);
    });
  });
});

import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import bcrypt from "bcryptjs";
import { Role } from "@prisma/client";

import { prisma } from "./db";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
            passwordHash: true,
            roles: true,
            emailVerified: true,
          },
        });

        if (!user) {
          return null;
        }

        // Check if user has a password (not OAuth-only user)
        if (!user.passwordHash) {
          return null;
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.passwordHash,
        );

        if (!isPasswordValid) {
          return null;
        }

        // Check if email is verified
        if (!user.emailVerified) {
          // Return a special error that the frontend can handle
          throw new Error("EMAIL_NOT_VERIFIED");
        }

        return {
          id: user.id,
          name: user.name || "",
          image: user.image,
          email: user.email,
          roles: user.roles,
          emailVerified: user.emailVerified,
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  events: {
    async createUser({ user }) {
      // For OAuth users, don't assign roles automatically - they'll choose during onboarding
      if (user.email) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            roles: [], // No roles assigned - user will select during onboarding
            emailVerified: new Date(), // OAuth emails are pre-verified
          },
        });
      }
    },
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      // Allow OAuth sign-ins
      if (account?.provider === "google") {
        // Check if user exists with this email
        const existingUser = await prisma.user.findUnique({
          where: { email: user.email! },
          include: {
            accounts: true,
          },
        });

        if (existingUser) {
          // Check if this Google account is already linked
          const existingGoogleAccount = existingUser.accounts?.find(
            (acc) =>
              acc.provider === "google" &&
              acc.providerAccountId === account.providerAccountId,
          );

          if (!existingGoogleAccount) {
            // Link the Google account to existing user
            await prisma.account.create({
              data: {
                userId: existingUser.id,
                type: account.type,
                provider: account.provider,
                providerAccountId: account.providerAccountId,
                refresh_token: account.refresh_token,
                access_token: account.access_token,
                expires_at: account.expires_at,
                token_type: account.token_type,
                scope: account.scope,
                id_token: account.id_token,
                session_state: account.session_state,
              },
            });
          }

          // Update user with OAuth info if missing
          if (!existingUser.name && profile?.name) {
            await prisma.user.update({
              where: { id: existingUser.id },
              data: {
                name: profile.name,
                image: profile.image,
                emailVerified: new Date(), // OAuth emails are verified
              },
            });
          }
        }

        return true;
      }

      // Allow credentials sign-ins (handled in authorize function)
      if (account?.provider === "credentials") {
        return true;
      }

      return false;
    },
    async jwt({ token, user }) {
      if (user) {
        token.name = user.name;
        token.roles = user.roles;
        token.emailVerified = user.emailVerified;
      }

      const dbUser = await prisma.user.findUnique({
        where: { email: token.email! },
        select: { roles: true, emailVerified: true },
      });

      if (dbUser) {
        token.roles = dbUser.roles;
        token.emailVerified = dbUser.emailVerified;
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.name = token.name;
        session.user.id = token.sub!;
        session.user.roles = token.roles as Role[];
        session.user.emailVerified = token.emailVerified as Date | null;
      }

      return session;
    },
  },
  pages: {
    signIn: "/login",
    error: "/auth/error",
  },
};

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function verifyPassword(
  password: string,
  hashedPassword: string,
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

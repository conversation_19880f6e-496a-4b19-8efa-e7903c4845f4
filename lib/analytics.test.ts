import { describe, it, expect, vi, beforeEach } from "vitest";
import { Decimal, PrismaPromise } from "@prisma/client/runtime/library";

import {
  getAdAnalytics,
  getUserAdAnalytics,
  getAppAnalytics,
  getUserAppAnalytics,
  getMonthlyAdAnalytics,
  getMonthlyAppAnalytics,
} from "@/lib/analytics";
import { prisma } from "@/lib/db";

// Mock the prisma client
vi.mock("@/lib/db", () => ({
  prisma: {
    adImpression: {
      findMany: vi.fn(),
    },
    advertisement: {
      findMany: vi.fn(),
    },
    app: {
      findMany: vi.fn(),
    },
  },
}));

describe("Analytics Functions", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getAdAnalytics", () => {
    it("should calculate analytics for an advertisement with CPC bidding", async () => {
      const mockImpressions = [
        {
          clicked: false,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
        },
      ];

      vi.mocked(prisma.adImpression.findMany).mockResolvedValue(
        mockImpressions as any,
      );

      const result = await getAdAnalytics("ad-123");

      expect(result).toEqual({
        impressions: 3,
        clicks: 2,
        spend: 1.0, // 2 clicks * $0.50
        ctr: "66.67",
      });
    });

    it("should calculate analytics for an advertisement with CPM bidding", async () => {
      const mockImpressions = Array.from({ length: 1500 }, (_, i) => ({
        clicked: i < 30, // 30 clicks out of 1500 impressions
        advertisement: { bidType: "CPM", bidAmount: new Decimal("2.00") },
      }));

      vi.mocked(prisma.adImpression.findMany).mockResolvedValue(
        mockImpressions as any,
      );

      const result = await getAdAnalytics("ad-123");

      expect(result).toEqual({
        impressions: 1500,
        clicks: 30,
        spend: 3.0, // 1500 impressions / 1000 * $2.00
        ctr: "2.00",
      });
    });

    it("should handle advertisement with no impressions", async () => {
      vi.mocked(prisma.adImpression.findMany).mockResolvedValue([]);

      const result = await getAdAnalytics("ad-123");

      expect(result).toEqual({
        impressions: 0,
        clicks: 0,
        spend: 0,
        ctr: "0.00",
      });
    });
  });

  describe("getUserAdAnalytics", () => {
    it("should aggregate analytics for all user advertisements", async () => {
      const mockAds = [
        {
          id: "ad-1",
          name: "Ad 1",
          impressions: [
            {
              clicked: false,
              advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
            },
            {
              clicked: true,
              advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
            },
          ],
        },
        {
          id: "ad-2",
          name: "Ad 2",
          impressions: [
            {
              clicked: false,
              advertisement: { bidType: "CPC", bidAmount: new Decimal("0.75") },
            },
            {
              clicked: true,
              advertisement: { bidType: "CPC", bidAmount: new Decimal("0.75") },
            },
          ],
        },
      ];

      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(
        mockAds as any,
      );

      // Mock individual ad analytics calls
      vi.mocked(prisma.adImpression.findMany)
        .mockResolvedValueOnce([
          {
            clicked: false,
            advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
          },
          {
            clicked: true,
            advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
          },
        ] as any)
        .mockResolvedValueOnce([
          {
            clicked: false,
            advertisement: { bidType: "CPC", bidAmount: new Decimal("0.75") },
          },
          {
            clicked: true,
            advertisement: { bidType: "CPC", bidAmount: new Decimal("0.75") },
          },
        ] as any);

      const result = await getUserAdAnalytics("user-123");

      expect(result.totalImpressions).toBe(4);
      expect(result.totalClicks).toBe(2);
      expect(result.totalSpend).toBe(1.25); // 0.50 + 0.75
      expect(result.averageCTR).toBe("50.00");
      expect(result.ads).toHaveLength(2);
    });
  });

  describe("getAppAnalytics", () => {
    it("should calculate analytics for an app with revenue share", async () => {
      const mockImpressions = [
        {
          clicked: false,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("1.00") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("1.00") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("1.00") },
        },
      ];

      vi.mocked(prisma.adImpression.findMany).mockResolvedValue(
        mockImpressions as any,
      );

      const result = await getAppAnalytics("app-123");

      expect(result).toEqual({
        impressions: 3,
        clicks: 2,
        revenue: 0.6, // 2 clicks * $1.00 * 30% revenue share
        ctr: "66.67",
      });
    });

    it("should calculate CPM revenue correctly", async () => {
      const mockImpressions = Array.from({ length: 2000 }, () => ({
        clicked: false,
        advertisement: { bidType: "CPM", bidAmount: new Decimal("5.00") },
      }));

      vi.mocked(prisma.adImpression.findMany).mockResolvedValue(
        mockImpressions as any,
      );

      const result = await getAppAnalytics("app-123");

      expect(result.revenue).toBeCloseTo(3.0, 2); // 2000/1000 * $5.00 * 30%
    });
  });

  describe("getUserAppAnalytics", () => {
    it("should aggregate analytics for all user apps", async () => {
      const mockApps = [
        { id: "app-1", name: "App 1" },
        { id: "app-2", name: "App 2" },
      ];

      vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);

      // Mock individual app analytics calls
      vi.mocked(prisma.adImpression.findMany)
        .mockResolvedValueOnce([
          {
            clicked: true,
            advertisement: { bidType: "CPC", bidAmount: new Decimal("1.00") },
          },
        ] as any)
        .mockResolvedValueOnce([
          {
            clicked: true,
            advertisement: { bidType: "CPC", bidAmount: new Decimal("2.00") },
          },
        ] as any);

      const result = await getUserAppAnalytics("user-123");

      expect(result.totalImpressions).toBe(2);
      expect(result.totalClicks).toBe(2);
      expect(result.totalRevenue).toBeCloseTo(0.9, 2); // (1.00 + 2.00) * 30%
      expect(result.apps).toHaveLength(2);
    });
  });

  describe("getMonthlyAdAnalytics", () => {
    it("should return monthly analytics data for advertisements", async () => {
      // Mock impressions for different months
      vi.mocked(prisma.adImpression.findMany).mockImplementation(
        ({ where }: any) => {
          const month = where.timestamp.gte.getMonth();

          if (month === 0) {
            return Promise.resolve([
              {
                clicked: false,
                advertisement: {
                  bidType: "CPC",
                  bidAmount: new Decimal("1.00"),
                },
              },
              {
                clicked: true,
                advertisement: {
                  bidType: "CPC",
                  bidAmount: new Decimal("1.00"),
                },
              },
            ]) as unknown as PrismaPromise<any[]>;
          }

          return Promise.resolve([]) as unknown as PrismaPromise<any[]>;
        },
      );

      const result = await getMonthlyAdAnalytics("user-123");

      expect(result).toHaveLength(12);
      expect(result[0]).toEqual({
        month: "Jan",
        impressions: 2,
        clicks: 1,
        spend: 1.0,
      });
      // Other months should have zero data
      expect(result[1].impressions).toBe(0);
    });
  });

  describe("getMonthlyAppAnalytics", () => {
    it("should return monthly analytics data for apps", async () => {
      // Mock impressions for different months
      vi.mocked(prisma.adImpression.findMany).mockImplementation(
        ({ where }: any): PrismaPromise<any[]> => {
          const month = where.timestamp.gte.getMonth();

          if (month === 0) {
            // January
            return Promise.resolve([
              {
                clicked: true,
                advertisement: {
                  bidType: "CPC",
                  bidAmount: new Decimal("2.00"),
                },
              },
            ]) as unknown as PrismaPromise<any[]>;
          }

          return Promise.resolve([]) as unknown as PrismaPromise<any[]>;
        },
      );

      const result = await getMonthlyAppAnalytics("user-123");

      expect(result).toHaveLength(12);
      expect(result[0]).toEqual({
        month: "Jan",
        impressions: 1,
        clicks: 1,
        revenue: 0.6, // $2.00 * 30%
      });
      // Other months should have zero data
      expect(result[1].impressions).toBe(0);
    });
  });
});

import { writeFile } from "fs/promises";
import { join } from "path";

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { nanoid } from "nanoid";

import { authOptions } from "@/lib/auth";

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
];

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        {
          error:
            "Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.",
        },
        { status: 400 },
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File too large. Maximum size is 5MB." },
        { status: 400 },
      );
    }

    // Generate unique filename
    const fileExtension = file.name.split(".").pop();
    const uniqueFilename = `${nanoid(16)}.${fileExtension}`;

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Save file to public/uploads directory
    const uploadPath = join(process.cwd(), "public", "uploads", uniqueFilename);

    await writeFile(uploadPath, buffer);

    // Return the public URL
    const fileUrl = `/uploads/${uniqueFilename}`;

    return NextResponse.json({
      message: "File uploaded successfully",
      url: fileUrl,
      filename: uniqueFilename,
      size: file.size,
      type: file.type,
    });
  } catch (error) {
    // Log error for debugging
    console.error("File upload error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: "File Upload API",
    maxSize: "5MB",
    allowedTypes: ALLOWED_TYPES,
    usage: {
      method: "POST",
      contentType: "multipart/form-data",
      field: "file",
      authentication: "Required (session)",
    },
  });
}

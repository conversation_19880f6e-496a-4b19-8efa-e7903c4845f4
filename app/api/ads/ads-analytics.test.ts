import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Decimal } from "@prisma/client/runtime/library";
import { getServerSession } from "next-auth";

import { GET } from "@/app/api/ads/route";
import { prisma } from "@/lib/db";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/db", () => ({
  prisma: {
    advertisement: {
      findMany: vi.fn(),
    },
    adImpression: {
      findMany: vi.fn(),
    },
  },
}));

describe("Ads API with Real Analytics", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return advertisements with real analytics data", async () => {
    // Mock session
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["ADVERTISER"],
      },
    } as any);

    // Mock advertisements
    const mockAds = [
      {
        id: "ad-1",
        name: "Test Ad 1",
        description: "Test description",
        imageUrl: null,
        productUrl: "https://example.com",
        targetTopics: ["AI", "tech"],
        budget: new Decimal("100.00"),
        bidType: "CPC",
        bidAmount: new Decimal("0.50"),
        status: "ACTIVE",
        createdAt: new Date("2024-01-01"),
      },
      {
        id: "ad-2",
        name: "Test Ad 2",
        description: "Test description 2",
        imageUrl: null,
        productUrl: "https://example2.com",
        targetTopics: ["productivity"],
        budget: new Decimal("200.00"),
        bidType: "CPM",
        bidAmount: new Decimal("2.00"),
        status: "ACTIVE",
        createdAt: new Date("2024-01-02"),
      },
    ];

    vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds as any);

    // Mock impressions for each ad
    vi.mocked(prisma.adImpression.findMany)
      .mockResolvedValueOnce([
        // Ad 1 impressions (CPC)
        {
          clicked: false,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("0.50") },
        },
      ] as any)
      .mockResolvedValueOnce([
        // Ad 2 impressions (CPM)
        ...Array.from({ length: 1000 }, (_, i) => ({
          clicked: i < 20, // 20 clicks out of 1000 impressions
          advertisement: { bidType: "CPM", bidAmount: new Decimal("2.00") },
        })),
      ] as any);

    const request = new NextRequest("http://localhost:3000/api/ads");
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.ads).toHaveLength(2);

    // Check first ad (CPC)
    expect(data.ads[0]).toMatchObject({
      id: "ad-1",
      name: "Test Ad 1",
      budget: 100,
      bidAmount: 0.5,
      impressions: 3,
      clicks: 2,
      spend: 1.0, // 2 clicks * $0.50
    });

    // Check second ad (CPM)
    expect(data.ads[1]).toMatchObject({
      id: "ad-2",
      name: "Test Ad 2",
      budget: 200,
      bidAmount: 2,
      impressions: 1000,
      clicks: 20,
      spend: 2.0, // 1000 impressions / 1000 * $2.00
    });
  });

  it("should return empty analytics for ads with no impressions", async () => {
    // Mock session
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["ADVERTISER"],
      },
    } as any);

    // Mock advertisement with no impressions
    const mockAds = [
      {
        id: "ad-1",
        name: "New Ad",
        description: "Brand new ad",
        imageUrl: null,
        productUrl: "https://example.com",
        targetTopics: ["AI"],
        budget: new Decimal("50.00"),
        bidType: "CPC",
        bidAmount: new Decimal("0.25"),
        status: "ACTIVE",
        createdAt: new Date("2024-01-01"),
      },
    ];

    vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds as any);
    vi.mocked(prisma.adImpression.findMany).mockResolvedValue([]);

    const request = new NextRequest("http://localhost:3000/api/ads");
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.ads).toHaveLength(1);
    expect(data.ads[0]).toMatchObject({
      id: "ad-1",
      name: "New Ad",
      impressions: 0,
      clicks: 0,
      spend: 0,
    });
  });

  it("should return 401 for unauthorized users", async () => {
    vi.mocked(getServerSession).mockResolvedValue(null);

    const request = new NextRequest("http://localhost:3000/api/ads");
    const response = await GET(request);

    expect(response.status).toBe(401);
  });

  it("should return 401 for users without ADVERTISER role", async () => {
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["MODEL_PROVIDER"],
      },
    } as any);

    const request = new NextRequest("http://localhost:3000/api/ads");
    const response = await GET(request);

    expect(response.status).toBe(401);
  });

  it("should handle database errors gracefully", async () => {
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["ADVERTISER"],
      },
    } as any);

    vi.mocked(prisma.advertisement.findMany).mockRejectedValue(
      new Error("Database error"),
    );

    const request = new NextRequest("http://localhost:3000/api/ads");
    const response = await GET(request);

    expect(response.status).toBe(500);
    expect(await response.json()).toEqual({
      error: "Internal server error",
    });
  });
});

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { Decimal } from "@prisma/client/runtime/library";

import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { advertisementSchema, validateData } from "@/lib/validation";
import { getAdAnalytics } from "@/lib/analytics";

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("ADVERTISER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const ads = await prisma.advertisement.findMany({
      where: {
        userId: session.user.id,
      },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get real analytics data for each advertisement
    const adsWithAnalytics = await Promise.all(
      ads.map(async (ad) => {
        const analytics = await getAdAnalytics(ad.id);

        return {
          ...ad,
          budget: Number(ad.budget),
          bidAmount: Number(ad.bidAmount),
          impressions: analytics.impressions,
          clicks: analytics.clicks,
          spend: analytics.spend || 0,
        };
      }),
    );

    return NextResponse.json({ ads: adsWithAnalytics });
  } catch (error) {
    console.error("Ads fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("ADVERTISER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(advertisementSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const {
      name,
      description,
      productUrl,
      imageUrl,
      targetTopics,
      budget,
      bidType,
      bidAmount,
    } = validation.data;

    const ad = await prisma.advertisement.create({
      data: {
        userId: session.user.id,
        name,
        description,
        productUrl,
        imageUrl: imageUrl || null,
        targetTopics: Array.isArray(targetTopics) ? targetTopics : [],
        budget: new Decimal(budget),
        bidType,
        bidAmount: new Decimal(bidAmount),
      },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        createdAt: true,
      },
    });

    return NextResponse.json(
      {
        message: "Advertisement created successfully",
        ad: {
          ...ad,
          budget: Number(ad.budget),
          bidAmount: Number(ad.bidAmount),
          impressions: 0,
          clicks: 0,
          spend: 0,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Ad creation error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

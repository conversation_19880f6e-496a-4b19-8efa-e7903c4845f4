import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { advertisementSchema, validateData } from "@/lib/validation";
import { AdService } from "@/services/ad";

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("ADVERTISER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Use AdService to get user ads
    const result = await AdService.getUserAds(session.user.id);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ ads: result.data.ads });
  } catch (error) {
    console.error("Ads fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("ADVERTISER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(advertisementSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const {
      name,
      description,
      productUrl,
      imageUrl,
      targetTopics,
      budget,
      bidType,
      bidAmount,
    } = validation.data;

    // Use AdService to create ad
    const result = await AdService.createAd({
      userId: session.user.id,
      name,
      description,
      productUrl,
      imageUrl: imageUrl || undefined,
      targetTopics,
      budget,
      bidType,
      bidAmount,
    });

    if (!result.success) {
      const statusCode = result.error?.includes("ADVERTISER") ? 403 : 500;

      return NextResponse.json({ error: result.error }, { status: statusCode });
    }

    return NextResponse.json(
      {
        message: result.data.message,
        ad: result.data.ad,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Ad creation error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

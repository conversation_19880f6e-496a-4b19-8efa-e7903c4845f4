import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";

import { POST } from "@/app/api/auth/verify-email/route";
import { AuthService } from "@/services/auth";

vi.mock("@/services/auth", () => ({
  AuthService: {
    verifyUserEmail: vi.fn(),
  },
}));

describe("/api/auth/verify-email", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/verify-email", () => {
    it("should verify email successfully with valid token", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: new Date(),
        roles: ["MODEL_PROVIDER"],
        createdAt: new Date(),
      };

      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue({
        success: true,
        data: {
          message:
            "Email verified successfully! You can now sign in to your account.",
          user: mockUser,
          welcomeEmailSent: true,
        },
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "Email verified successfully! You can now sign in to your account.",
      );
      expect(data.user).toMatchObject({
        id: "user-123",
        email: "<EMAIL>",
      });
      expect(data.user.emailVerified).toBeTruthy();
      expect(data.welcomeEmailSent).toBe(true);

      expect(AuthService.verifyUserEmail).toHaveBeenCalledWith("valid-token");
    });

    it("should return 400 for missing token", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({}),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe(
        "Invalid input: expected string, received undefined",
      );
    });

    it("should return 400 for invalid token", async () => {
      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue({
        success: false,
        error: "Invalid or expired verification token",
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "invalid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid or expired verification token");
    });

    it("should return 400 for expired token", async () => {
      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue({
        success: false,
        error: "Invalid or expired verification token",
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "expired-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid or expired verification token");
    });

    it("should return 400 for already verified email", async () => {
      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue({
        success: false,
        error: "Email is already verified",
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Email is already verified");
    });

    it("should handle welcome email failure gracefully", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: new Date(),
        roles: ["MODEL_PROVIDER"],
        createdAt: new Date(),
      };

      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue({
        success: true,
        data: {
          message:
            "Email verified successfully! You can now sign in to your account.",
          user: mockUser,
          welcomeEmailSent: false,
        },
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "Email verified successfully! You can now sign in to your account.",
      );
      expect(data.welcomeEmailSent).toBe(false);
    });

    it("should handle service errors", async () => {
      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new Error("Service error"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Internal server error");
    });
  });
});

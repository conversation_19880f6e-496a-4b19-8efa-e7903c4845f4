import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";

import { POST } from "@/app/api/auth/verify-email/route";
import { prisma } from "@/lib/db";
import { sendWelcomeEmail } from "@/lib/email";

vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findFirst: vi.fn(),
      update: vi.fn(),
    },
  },
}));

vi.mock("@/lib/email", () => ({
  sendWelcomeEmail: vi.fn(),
}));

describe("/api/auth/verify-email", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/verify-email", () => {
    it("should verify email successfully with valid token", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: false,
        emailVerificationToken: "valid-token",
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

      const mockUpdatedUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: true,
        roles: ["MODEL_PROVIDER"],
        createdAt: new Date(),
      };

      vi.mocked(prisma.user.findFirst).mockResolvedValue(mockUser as any);
      vi.mocked(prisma.user.update).mockResolvedValue(mockUpdatedUser as any);
      vi.mocked(sendWelcomeEmail).mockResolvedValue({ success: true });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "Email verified successfully! You can now sign in to your account.",
      );
      expect(data.user).toMatchObject({
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: true,
      });
      expect(data.welcomeEmailSent).toBe(true);

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: {
          emailVerified: expect.any(Date),
          emailVerificationToken: null,
          emailVerificationExpires: null,
        },
        select: {
          id: true,
          email: true,
          emailVerified: true,
          roles: true,
          createdAt: true,
        },
      });

      expect(sendWelcomeEmail).toHaveBeenCalledWith("<EMAIL>");
    });

    it("should return 400 for missing token", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({}),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe(
        "Invalid input: expected string, received undefined",
      );
    });

    it("should return 400 for invalid token", async () => {
      vi.mocked(prisma.user.findFirst).mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "invalid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid or expired verification token");
    });

    it("should return 400 for expired token", async () => {
      vi.mocked(prisma.user.findFirst).mockResolvedValue(null); // Expired tokens won't be found

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "expired-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid or expired verification token");
    });

    it("should return 200 for already verified email", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: true,
        emailVerificationToken: "valid-token",
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

      vi.mocked(prisma.user.findFirst).mockResolvedValue(mockUser as any);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("Email is already verified");
    });

    it("should handle welcome email failure gracefully", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: false,
        emailVerificationToken: "valid-token",
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

      const mockUpdatedUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: true,
        roles: ["MODEL_PROVIDER"],
        createdAt: new Date(),
      };

      vi.mocked(prisma.user.findFirst).mockResolvedValue(mockUser as any);
      vi.mocked(prisma.user.update).mockResolvedValue(mockUpdatedUser as any);
      vi.mocked(sendWelcomeEmail).mockResolvedValue({
        success: false,
        error: "Email service unavailable",
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "Email verified successfully! You can now sign in to your account.",
      );
      expect(data.welcomeEmailSent).toBe(false);
    });

    it("should handle database errors", async () => {
      vi.mocked(prisma.user.findFirst).mockRejectedValue(
        new Error("Database error"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          body: JSON.stringify({
            token: "valid-token",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Internal server error");
    });
  });
});

import { NextRequest, NextResponse } from "next/server";

import {
  emailVerificationSchema,
  validateData,
  rateLimitConfigs,
} from "@/lib/validation";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { AuthService } from "@/services/auth";

async function verifyEmailHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(emailVerificationSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { token } = validation.data;

    // Use AuthService to verify email
    const result = await AuthService.verifyUserEmail(token);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json(
      {
        message: result.data.message,
        user: result.data.user,
        welcomeEmailSent: result.data.welcomeEmailSent,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Email verification error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(verifyEmailHandler);

// GET endpoint for URL-based verification (when user clicks link)
async function verifyEmailGetHandler(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { error: "Verification token is required" },
        { status: 400 },
      );
    }

    // Use AuthService to verify email
    const result = await AuthService.verifyUserEmail(token);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json(
      {
        message: result.data.message,
        user: result.data.user,
        welcomeEmailSent: result.data.welcomeEmailSent,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Email verification error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export const GET = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(verifyEmailGetHandler);

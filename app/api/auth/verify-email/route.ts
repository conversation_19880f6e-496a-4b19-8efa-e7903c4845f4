import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/db";
import {
  emailVerificationSchema,
  validateData,
  rateLimitConfigs,
} from "@/lib/validation";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { sendWelcomeEmail } from "@/lib/email";

async function verifyEmailHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(emailVerificationSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { token } = validation.data;

    // Find user with this verification token
    const user = await prisma.user.findFirst({
      where: {
        emailVerificationToken: token,
        emailVerificationExpires: {
          gt: new Date(), // Token must not be expired
        },
      },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        emailVerificationToken: true,
        emailVerificationExpires: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Invalid or expired verification token" },
        { status: 400 },
      );
    }

    if (user.emailVerified) {
      return NextResponse.json(
        { message: "Email is already verified" },
        { status: 200 },
      );
    }

    // Update user to mark email as verified and clear verification fields
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: new Date(),
        emailVerificationToken: null,
        emailVerificationExpires: null,
      },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        roles: true,
        createdAt: true,
      },
    });

    // Send welcome email
    const welcomeEmailResult = await sendWelcomeEmail(updatedUser.email);

    if (!welcomeEmailResult.success) {
      console.error("Failed to send welcome email:", welcomeEmailResult.error);
    }

    return NextResponse.json(
      {
        message:
          "Email verified successfully! You can now sign in to your account.",
        user: updatedUser,
        welcomeEmailSent: welcomeEmailResult.success,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Email verification error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(verifyEmailHandler);

// GET endpoint for URL-based verification (when user clicks link)
async function verifyEmailGetHandler(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { error: "Verification token is required" },
        { status: 400 },
      );
    }

    // Find user with this verification token
    const user = await prisma.user.findFirst({
      where: {
        emailVerificationToken: token,
        emailVerificationExpires: {
          gt: new Date(), // Token must not be expired
        },
      },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        emailVerificationToken: true,
        emailVerificationExpires: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Invalid or expired verification token" },
        { status: 400 },
      );
    }

    if (user.emailVerified) {
      return NextResponse.json(
        { message: "Email is already verified" },
        { status: 200 },
      );
    }

    // Update user to mark email as verified and clear verification fields
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: new Date(),
        emailVerificationToken: null,
        emailVerificationExpires: null,
      },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        roles: true,
        createdAt: true,
      },
    });

    // Send welcome email
    const welcomeEmailResult = await sendWelcomeEmail(updatedUser.email);

    if (!welcomeEmailResult.success) {
      console.error("Failed to send welcome email:", welcomeEmailResult.error);
    }

    return NextResponse.json(
      {
        message:
          "Email verified successfully! You can now sign in to your account.",
        user: updatedUser,
        welcomeEmailSent: welcomeEmailResult.success,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Email verification error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export const GET = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(verifyEmailGetHandler);

import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/db";
import {
  resendVerificationSchema,
  validateData,
  sanitizeEmail,
  rateLimitConfigs,
} from "@/lib/validation";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import {
  generateVerificationToken,
  getVerificationExpiry,
  sendVerificationEmail,
} from "@/lib/email";

async function resendVerificationHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(resendVerificationSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { email } = validation.data;
    const sanitizedEmail = sanitizeEmail(email);

    // Find user with this email
    const user = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        emailVerificationToken: true,
        emailVerificationExpires: true,
      },
    });

    if (!user) {
      // Don't reveal if user exists or not for security
      return NextResponse.json(
        {
          message:
            "If an account with this email exists and is not verified, a verification email has been sent.",
        },
        { status: 200 },
      );
    }

    if (user.emailVerified) {
      return NextResponse.json(
        { error: "Email is already verified" },
        { status: 400 },
      );
    }

    // Check if user is already verified (token is null when verified)
    if (!user.emailVerificationToken) {
      return NextResponse.json(
        { error: "Email is already verified" },
        { status: 400 },
      );
    }
    // Check if there's a recent verification email sent (rate limiting)
    if (
      user.emailVerificationExpires &&
      user.emailVerificationExpires > new Date(Date.now() - 0.00005 * 60 * 1000)
    ) {
      return NextResponse.json(
        {
          error:
            "A verification email was recently sent. Please wait 30 seconds before requesting another.",
        },
        { status: 429 },
      );
    }

    // Generate new verification token
    const verificationToken = generateVerificationToken();
    const verificationExpires = getVerificationExpiry();

    // Update user with new verification token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerificationToken: verificationToken,
        emailVerificationExpires: verificationExpires,
      },
    });

    // Send verification email
    const emailResult = await sendVerificationEmail({
      email: sanitizedEmail,
      token: verificationToken,
    });

    if (!emailResult.success) {
      console.error("Failed to send verification email:", emailResult.error);

      return NextResponse.json(
        { error: "Failed to send verification email. Please try again later." },
        { status: 500 },
      );
    }

    return NextResponse.json(
      {
        message:
          "Verification email sent successfully! Please check your email.",
        emailSent: true,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Resend verification error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(resendVerificationHandler);

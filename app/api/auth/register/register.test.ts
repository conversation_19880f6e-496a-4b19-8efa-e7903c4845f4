import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import bcrypt from "bcryptjs";
import { Role } from "@prisma/client";

import { prisma } from "@/lib/db";
import { sendVerificationEmail } from "@/lib/email";

vi.mock("@/lib/email", () => ({
  sendVerificationEmail: vi.fn(),
  generateVerificationToken: vi.fn(() => "mock-token"),
  getVerificationExpiry: vi.fn(
    () => new Date(Date.now() + 24 * 60 * 60 * 1000),
  ),
}));

// Import the handler function directly to avoid middleware issues
async function registerHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Mock validation
    const { email, password, roles } = body;
    const sanitizedEmail = email.toLowerCase();

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
    });

    if (existingUser) {
      return Response.json(
        { error: "User with this email already exists" },
        { status: 409 },
      );
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Generate email verification token
    const verificationToken = "mock-token";
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);

    // Create user with email verification fields
    const user = await prisma.user.create({
      data: {
        email: sanitizedEmail,
        passwordHash,
        roles: roles,
        emailVerified: new Date(),
        emailVerificationToken: verificationToken,
        emailVerificationExpires: verificationExpires,
      },
      select: {
        id: true,
        email: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
      },
    });

    // Send verification email
    const emailResult = await sendVerificationEmail({
      email: sanitizedEmail,
      token: verificationToken,
    });

    return Response.json(
      {
        message:
          "Account created successfully! Please check your email to verify your account before signing in.",
        user: {
          id: user.id,
          email: user.email,
          roles: user.roles,
          emailVerified: user.emailVerified,
          createdAt: user.createdAt,
        },
        emailSent: emailResult.success,
      },
      { status: 201 },
    );
  } catch {
    // Log error for debugging in tests
    // console.error("Registration error:", error);

    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

describe("/api/auth/register", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should register a new user successfully with email verification", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      passwordHash: "hashed-password",
      roles: [Role.MODEL_PROVIDER],
      emailVerified: null,
      emailVerificationToken: "mock-token",
      emailVerificationExpires: new Date("2025-06-16T05:20:29.391Z"),
      name: null,
      image: null,
      createdAt: new Date("2025-06-15T05:20:29.391Z"),
      updatedAt: new Date("2025-06-15T05:20:29.391Z"),
    };

    // Mock bcrypt
    vi.mocked(bcrypt.hash).mockResolvedValue("hashed-password" as any);

    // Mock Prisma
    vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
    vi.mocked(prisma.user.create).mockResolvedValue(mockUser);

    // Mock email service
    vi.mocked(sendVerificationEmail).mockResolvedValue({ success: true });

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await registerHandler(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data.message).toBe(
      "Account created successfully! Please check your email to verify your account before signing in.",
    );
    expect(data.user).toEqual({
      id: mockUser.id,
      email: mockUser.email,
      roles: mockUser.roles,
      emailVerified: null,
      createdAt: expect.any(String), // Handle date serialization
    });
    expect(data.emailSent).toBe(true);

    expect(sendVerificationEmail).toHaveBeenCalledWith({
      email: "<EMAIL>",
      token: "mock-token",
    });
  });

  it("should return 409 if user already exists", async () => {
    vi.mocked(prisma.user.findUnique).mockResolvedValue({
      id: "existing-user",
      email: "<EMAIL>",
      passwordHash: "hash",
      roles: ["MODEL_PROVIDER"],
      emailVerified: null,
      emailVerificationToken: null,
      emailVerificationExpires: null,
      name: null,
      image: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await registerHandler(request);
    const data = await response.json();

    expect(response.status).toBe(409);
    expect(data.error).toBe("User with this email already exists");
  });

  it("should handle database errors", async () => {
    vi.mocked(bcrypt.hash).mockResolvedValue();
    vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
    vi.mocked(prisma.user.create).mockRejectedValue(
      new Error("Database error"),
    );

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await registerHandler(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe("Internal server error");
  });

  it("should handle email sending failure gracefully", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      passwordHash: "hashed-password",
      roles: [Role.MODEL_PROVIDER],
      emailVerified: null,
      emailVerificationToken: "mock-token",
      emailVerificationExpires: new Date("2025-06-16T05:20:29.391Z"),
      name: null,
      image: null,
      createdAt: new Date("2025-06-15T05:20:29.391Z"),
      updatedAt: new Date("2025-06-15T05:20:29.391Z"),
    };

    // Mock bcrypt
    vi.mocked(bcrypt.hash).mockResolvedValue("hashed-password" as any);

    // Mock Prisma
    vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
    vi.mocked(prisma.user.create).mockResolvedValue(mockUser);

    // Mock email service failure
    vi.mocked(sendVerificationEmail).mockResolvedValue({
      success: false,
      error: "Email service unavailable",
    });

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await registerHandler(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data.message).toBe(
      "Account created successfully! Please check your email to verify your account before signing in.",
    );
    expect(data.emailSent).toBe(false);
  });
});

import { NextRequest, NextResponse } from "next/server";
import { Role } from "@prisma/client";

import { prisma } from "@/lib/db";
import { hashPassword } from "@/lib/auth";
import {
  registerSchema,
  validateData,
  sanitizeEmail,
  rateLimitConfigs,
} from "@/lib/validation";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import {
  generateVerificationToken,
  getVerificationExpiry,
  sendVerificationEmail,
} from "@/lib/email";

async function registerHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(registerSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { email, password, roles } = validation.data;

    // Sanitize email
    const sanitizedEmail = sanitizeEmail(email);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists, please sign in." },
        { status: 409 },
      );
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Generate email verification token
    const verificationToken = generateVerificationToken();
    const verificationExpires = getVerificationExpiry();

    // Create user with email verification fields
    const user = await prisma.user.create({
      data: {
        email: sanitizedEmail,
        passwordHash,
        roles: roles as Role[],
        emailVerified: null,
        emailVerificationToken: verificationToken,
        emailVerificationExpires: verificationExpires,
      },
      select: {
        id: true,
        email: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
      },
    });

    // Send verification email
    const emailResult = await sendVerificationEmail({
      email: sanitizedEmail,
      token: verificationToken,
    });

    if (!emailResult.success) {
      // Log the error but don't fail the registration

      console.error("Failed to send verification email:", emailResult.error);
    }

    return NextResponse.json(
      {
        message:
          "Account created successfully! Please check your email to verify your account before signing in.",
        user: {
          id: user.id,
          email: user.email,
          roles: user.roles,
          emailVerified: user.emailVerified,
          createdAt: user.createdAt,
        },
        emailSent: emailResult.success,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Registration error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(registerHandler);

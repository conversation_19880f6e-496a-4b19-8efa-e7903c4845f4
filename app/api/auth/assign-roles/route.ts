import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";
import { Role } from "@prisma/client";

import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { rateLimitConfigs } from "@/lib/validation";

// Validation schema
const assignRolesSchema = z.object({
  roles: z
    .array(z.enum(["MODEL_PROVIDER", "ADVERTISER"]))
    .min(1, "At least one role is required")
    .max(2, "Maximum two roles allowed"),
});

async function assignRolesHandler(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = assignRolesSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid input",
          details: validationResult.error.message,
        },
        { status: 400 },
      );
    }

    const { roles } = validationResult.data;

    // Check if user exists and doesn't already have roles
    const existingUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        roles: true,
        emailVerified: true,
      },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user already has roles assigned
    if (existingUser.roles.length > 0) {
      return NextResponse.json(
        { error: "User already has roles assigned" },
        { status: 409 },
      );
    }

    // Update user with selected roles
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        roles: roles as Role[],
      },
      select: {
        id: true,
        email: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
      },
    });

    return NextResponse.json(
      {
        message: "Roles assigned successfully",
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          roles: updatedUser.roles,
          emailVerified: updatedUser.emailVerified,
          createdAt: updatedUser.createdAt,
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Role assignment error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(assignRolesHandler);

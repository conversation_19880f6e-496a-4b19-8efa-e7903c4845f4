import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { Role } from "@prisma/client";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
  },
}));

vi.mock("@/lib/middleware", () => ({
  withMiddleware: vi.fn((fn) => fn),
  withSecurityHeaders: vi.fn(() => (fn: any) => fn),
  withRateLimit: vi.fn(() => (fn: any) => fn),
}));

vi.mock("@/lib/validation", () => ({
  rateLimitConfigs: {
    auth: {},
  },
}));

import { POST } from "./route";

import { prisma } from "@/lib/db";

describe("/api/auth/assign-roles", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should assign roles to authenticated user successfully", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [],
      emailVerified: true,
    };

    const updatedUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [Role.MODEL_PROVIDER],
      emailVerified: true,
      createdAt: new Date(),
    };

    // Mock session
    vi.mocked(getServerSession).mockResolvedValue({
      user: { id: "user-123", email: "<EMAIL>", roles: [] },
    } as any);

    // Mock Prisma
    vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
    vi.mocked(prisma.user.update).mockResolvedValue(updatedUser as any);

    const request = new NextRequest(
      "http://localhost:3000/api/auth/assign-roles",
      {
        method: "POST",
        body: JSON.stringify({
          roles: ["MODEL_PROVIDER"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.message).toBe("Roles assigned successfully");
    expect(data.user.roles).toEqual([Role.MODEL_PROVIDER]);

    expect(prisma.user.update).toHaveBeenCalledWith({
      where: { id: "user-123" },
      data: {
        roles: ["MODEL_PROVIDER"],
      },
      select: {
        id: true,
        email: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
      },
    });
  });

  it("should assign multiple roles successfully", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [],
      emailVerified: true,
    };

    const updatedUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
      emailVerified: true,
      createdAt: new Date(),
    };

    vi.mocked(getServerSession).mockResolvedValue({
      user: { id: "user-123", email: "<EMAIL>", roles: [] },
    } as any);

    vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
    vi.mocked(prisma.user.update).mockResolvedValue(updatedUser as any);

    const request = new NextRequest(
      "http://localhost:3000/api/auth/assign-roles",
      {
        method: "POST",
        body: JSON.stringify({
          roles: ["MODEL_PROVIDER", "ADVERTISER"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.user.roles).toEqual([Role.MODEL_PROVIDER, Role.ADVERTISER]);
  });

  it("should return 401 for unauthenticated requests", async () => {
    vi.mocked(getServerSession).mockResolvedValue(null);

    const request = new NextRequest(
      "http://localhost:3000/api/auth/assign-roles",
      {
        method: "POST",
        body: JSON.stringify({
          roles: ["MODEL_PROVIDER"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe("Authentication required");
  });

  it("should return 400 for invalid roles", async () => {
    vi.mocked(getServerSession).mockResolvedValue({
      user: { id: "user-123", email: "<EMAIL>", roles: [] },
    } as any);

    const request = new NextRequest(
      "http://localhost:3000/api/auth/assign-roles",
      {
        method: "POST",
        body: JSON.stringify({
          roles: ["INVALID_ROLE"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe("Invalid input");
  });

  it("should return 409 if user already has roles", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [Role.MODEL_PROVIDER], // User already has roles
      emailVerified: true,
    };

    vi.mocked(getServerSession).mockResolvedValue({
      user: { id: "user-123", email: "<EMAIL>", roles: [] },
    } as any);

    vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

    const request = new NextRequest(
      "http://localhost:3000/api/auth/assign-roles",
      {
        method: "POST",
        body: JSON.stringify({
          roles: ["ADVERTISER"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(409);
    expect(data.error).toBe("User already has roles assigned");
  });

  it("should return 404 if user not found", async () => {
    vi.mocked(getServerSession).mockResolvedValue({
      user: { id: "user-123", email: "<EMAIL>", roles: [] },
    } as any);

    vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

    const request = new NextRequest(
      "http://localhost:3000/api/auth/assign-roles",
      {
        method: "POST",
        body: JSON.stringify({
          roles: ["MODEL_PROVIDER"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(404);
    expect(data.error).toBe("User not found");
  });

  it("should handle database errors", async () => {
    vi.mocked(getServerSession).mockResolvedValue({
      user: { id: "user-123", email: "<EMAIL>", roles: [] },
    } as any);

    vi.mocked(prisma.user.findUnique).mockRejectedValue(
      new Error("Database error"),
    );

    const request = new NextRequest(
      "http://localhost:3000/api/auth/assign-roles",
      {
        method: "POST",
        body: JSON.stringify({
          roles: ["MODEL_PROVIDER"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe("Internal server error");
  });
});

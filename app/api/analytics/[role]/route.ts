// app/api/analytics/[role]/route.ts

// 1️⃣ Prevent any static prerender/analysis—this runs at request time
export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";
import {
  getUserAppAnalytics,
  getUserAdAnalytics,
  getMonthlyAppAnalytics,
  getMonthlyAdAnalytics,
} from "@/lib/analytics";

// ── Shapes for your Prisma selects ─────────────────────────────────────
type AppRecord = {
  id: string;
  name: string;
  createdAt: Date;
};

type AdRecord = {
  id: string;
  name: string;
  budget: unknown; // whatever Prisma.client gives you
  bidType: string; // your enum comes through as a string
  bidAmount: unknown; // whatever Prisma.client gives you
  createdAt: Date;
};

// ── Shape of your analytics SD<PERSON>’s ads array (ctr is a string) ─────────
type AnalyticsAd = {
  id: string;
  impressions: number;
  clicks: number;
  spend?: number;
  ctr: string;
};

export async function GET(request: NextRequest) {
  try {
    // ── 1. AUTH ─────────────────────────────────────────────────────────
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // ── 2. EXTRACT & VALIDATE [role] ───────────────────────────────────
    const segments = request.nextUrl.pathname.split("/");
    const role = segments[segments.length - 1];

    if (role !== "model" && role !== "advertiser") {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 });
    }

    // ── 3. PERMISSION CHECK ─────────────────────────────────────────────
    const requiredRole = role === "model" ? "MODEL_PROVIDER" : "ADVERTISER";

    if (!session.user.roles.includes(requiredRole as any)) {
      return NextResponse.json(
        { error: "Unauthorized for this role" },
        { status: 403 },
      );
    }

    // ── 4a. MODEL branch ────────────────────────────────────────────────
    if (role === "model") {
      const apps = (await prisma.app.findMany({
        where: { userId: session.user.id },
        select: { id: true, name: true, createdAt: true },
      })) as AppRecord[];

      const userAnalytics = await getUserAppAnalytics(session.user.id);
      const monthlyData = await getMonthlyAppAnalytics(session.user.id);

      const analytics = {
        totalApps: apps.length,
        totalImpressions: userAnalytics.totalImpressions,
        totalClicks: userAnalytics.totalClicks,
        totalRevenue: userAnalytics.totalRevenue,
        monthlyData,
        topApps: userAnalytics.apps.slice(0, 5).map((app) => {
          const found = apps.find((a) => a.id === app.id)!;

          return {
            id: app.id,
            name: found.name,
            createdAt: found.createdAt,
            impressions: app.impressions,
            clicks: app.clicks,
            revenue: app.revenue,
          };
        }),
      };

      return NextResponse.json({ analytics });
    }

    // ── 4b. ADVERTISER branch ────────────────────────────────────────────
    const ads = (await prisma.advertisement.findMany({
      where: { userId: session.user.id },
      select: {
        id: true,
        name: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        createdAt: true,
      },
    })) as AdRecord[];

    // Coerce budget → number
    const totalBudget = ads.reduce(
      (sum: number, ad) => sum + Number(ad.budget),
      0,
    );

    const userAnalytics = await getUserAdAnalytics(session.user.id);
    const monthlyData = await getMonthlyAdAnalytics(session.user.id);

    const analytics = {
      totalCampaigns: ads.length,
      totalBudget,
      totalSpend: userAnalytics.totalSpend,
      totalImpressions: userAnalytics.totalImpressions,
      totalClicks: userAnalytics.totalClicks,
      averageCTR: userAnalytics.averageCTR,
      monthlyData,
      topCampaigns: (userAnalytics.ads as AnalyticsAd[])
        .slice(0, 5)
        .map((ad) => {
          const found = ads.find((a) => a.id === ad.id)!;

          return {
            id: ad.id,
            name: found.name,
            budget: Number(found.budget),
            bidType: found.bidType,
            bidAmount: Number(found.bidAmount),
            createdAt: found.createdAt,
            impressions: ad.impressions,
            clicks: ad.clicks,
            spend: ad.spend ?? 0,
            ctr: parseFloat(ad.ctr),
          };
        }),
    };

    return NextResponse.json({ analytics });
  } catch (error) {
    console.error("Analytics fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

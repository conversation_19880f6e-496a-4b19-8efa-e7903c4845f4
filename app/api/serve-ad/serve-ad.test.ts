import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Decimal } from "@prisma/client/runtime/library";
import { AppStatus, AdStatus, BidType } from "@prisma/client";

vi.mock("@/lib/db", () => ({
  prisma: {
    app: {
      findUnique: vi.fn(),
    },
    advertisement: {
      findMany: vi.fn(),
    },
    adImpression: {
      create: vi.fn(),
    },
  },
}));

import { GET, POST } from "@/app/api/serve-ad/route";
import { prisma } from "@/lib/db";

describe("/api/serve-ad", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET /api/serve-ad", () => {
    it("should return API documentation", async () => {
      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("AI Ad Platform - Ad Serving API");
      expect(data.version).toBe("1.0.0");
      expect(data.endpoints).toBeDefined();
    });
  });

  describe("POST /api/serve-ad", () => {
    const mockApp = {
      id: "app-1",
      userId: "user-123",
      name: "Test App",
      appId: "app_mock-id-12345",
      appSecret: "secret_mock-secret-12345",
      description: "Test description",
      status: AppStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockAds = [
      {
        id: "ad-1",
        userId: "user-123",
        name: "Test Ad",
        description: "Test description",
        imageUrl: null,
        productUrl: "https://example.com",
        targetTopics: ["AI", "productivity"],
        budget: new Decimal(100),
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
        status: AdStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    it("should serve an ad successfully", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp);
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds);

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        body: JSON.stringify({
          appId: "app_mock-id-12345",
          appSecret: "secret_mock-secret-12345",
          topics: ["AI", "productivity"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.ad).toMatchObject({
        id: "ad-1",
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com",
        targetTopics: ["AI", "productivity"],
      });
      expect(data.appId).toBe("app-1");
      expect(data.trackingUrl).toContain("/api/impressions");
      expect(data.instructions).toBeDefined();
    });

    it("should return 400 for missing credentials", async () => {
      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        body: JSON.stringify({
          appId: "app_mock-id-12345",
          // Missing appSecret
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("appId and appSecret are required");
    });

    it("should return 401 for invalid credentials", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        body: JSON.stringify({
          appId: "invalid-app-id",
          appSecret: "invalid-secret",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Invalid credentials or inactive app");
    });

    it("should return 401 for wrong app secret", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue({
        ...mockApp,
        appSecret: "different-secret",
      });

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        body: JSON.stringify({
          appId: "app_mock-id-12345",
          appSecret: "wrong-secret",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Invalid credentials or inactive app");
    });

    it("should return 401 for inactive app", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue({
        ...mockApp,
        status: "SUSPENDED",
      });

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        body: JSON.stringify({
          appId: "app_mock-id-12345",
          appSecret: "secret_mock-secret-12345",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Invalid credentials or inactive app");
    });

    it("should return 204 when no ads are available", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp);
      vi.mocked(prisma.advertisement.findMany)
        .mockResolvedValueOnce([]) // First call for targeted ads
        .mockResolvedValueOnce([]); // Second call for fallback ads

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        body: JSON.stringify({
          appId: "app_mock-id-12345",
          appSecret: "secret_mock-secret-12345",
          topics: ["AI"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);

      expect(response.status).toBe(204);
    });

    it("should serve fallback ads when no targeted ads found", async () => {
      const fallbackAds = [
        {
          id: "ad-fallback",
          userId: "user-123",
          name: "Fallback Ad",
          description: "Fallback description",
          imageUrl: null,
          productUrl: "https://fallback.com",
          targetTopics: ["general"],
          budget: new Decimal(100),
          bidType: BidType.CPM,
          bidAmount: new Decimal(1.0),
          status: AdStatus.ACTIVE,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp);
      vi.mocked(prisma.advertisement.findMany)
        .mockResolvedValueOnce([]) // First call for targeted ads
        .mockResolvedValueOnce(fallbackAds); // Second call for fallback ads

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        body: JSON.stringify({
          appId: "app_mock-id-12345",
          appSecret: "secret_mock-secret-12345",
          topics: ["nonexistent-topic"],
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.ad).toMatchObject({
        id: "ad-fallback",
        name: "Fallback Ad",
        description: "Fallback description",
        productUrl: "https://fallback.com",
        targetTopics: ["general"],
      });
    });
  });
});

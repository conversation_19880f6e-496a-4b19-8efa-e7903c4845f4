import { NextRequest } from "next/server";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { getServerSession } from "next-auth";
import { AppStatus } from "@prisma/client";

import { getAppAnalytics } from "@/lib/analytics";
import { GET, POST } from "@/app/api/apps/route";
import { prisma } from "@/lib/db";

vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/db", () => ({
  prisma: {
    app: {
      findMany: vi.fn(),
      create: vi.fn(),
    },
  },
}));

vi.mock("@/lib/analytics", () => ({
  getAppAnalytics: vi.fn(),
}));

vi.mock("nanoid", () => ({
  nanoid: vi.fn(() => "mock-id-12345"),
}));

describe("/api/apps", () => {
  const mockSession = {
    user: {
      id: "user-123",
      email: "<EMAIL>",
      roles: ["MODEL_PROVIDER"],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET /api/apps", () => {
    it("should return apps for authenticated model provider", async () => {
      const mockApps = [
        {
          id: "app-1",
          userId: "user-123",
          name: "Test App",
          appId: "app_mock-id-12345",
          appSecret: "secret_mock-secret-12345",
          status: AppStatus.ACTIVE,
          createdAt: new Date("2025-06-15T05:20:29.391Z"),
          updatedAt: new Date("2025-06-15T05:20:29.391Z"),
          description: "Test description",
        },
      ];

      const mockAnalytics = {
        impressions: 100,
        clicks: 10,
        revenue: 50.0,
        ctr: "10.00",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps);
      vi.mocked(getAppAnalytics).mockResolvedValue(mockAnalytics);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.apps).toHaveLength(1);
      expect(data.apps[0]).toMatchObject({
        id: mockApps[0].id,
        name: mockApps[0].name,
        appId: mockApps[0].appId,
        status: mockApps[0].status,
        createdAt: mockApps[0].createdAt.toISOString(),
        description: mockApps[0].description,
        impressions: mockAnalytics.impressions,
        clicks: mockAnalytics.clicks,
        revenue: mockAnalytics.revenue,
        ctr: mockAnalytics.ctr,
      });
    });

    it("should return 401 for unauthenticated user", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
    });

    it("should return 401 for user without MODEL_PROVIDER role", async () => {
      const sessionWithoutRole = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(sessionWithoutRole);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
    });
  });

  describe("POST /api/apps", () => {
    it("should create a new app successfully", async () => {
      const mockApp = {
        id: "app-1",
        userId: "user-123",
        name: "Test App",
        appId: "app_mock-id-12345",
        appSecret: "secret_mock-secret-12345",
        status: AppStatus.ACTIVE,
        createdAt: new Date("2025-06-15T05:20:29.391Z"),
        updatedAt: new Date("2025-06-15T05:20:29.391Z"),
        description: "Test description",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(prisma.app.create).mockResolvedValue(mockApp);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        body: JSON.stringify({
          name: "Test App",
          description: "Test description",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.message).toBe("App created successfully");
      expect(data.app).toMatchObject({
        id: mockApp.id,
        name: mockApp.name,
        appId: mockApp.appId,
        status: mockApp.status,
        createdAt: mockApp.createdAt.toISOString(),
        description: mockApp.description,
        impressions: 0,
        clicks: 0,
        revenue: 0,
      });
    });

    it("should return 400 for missing required fields", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        body: JSON.stringify({}),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("App name is required");
    });

    it("should handle database errors", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(prisma.app.create).mockRejectedValue(
        new Error("Database error"),
      );

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        body: JSON.stringify({
          name: "Test App",
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Internal server error");
    });
  });
});

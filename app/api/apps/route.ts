import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { nanoid } from "nanoid";

import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { appSchema, validateData } from "@/lib/validation";
import { getAppAnalytics } from "@/lib/analytics";

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const apps = await prisma.app.findMany({
      where: {
        userId: session.user.id,
      },
      select: {
        id: true,
        name: true,
        appId: true,
        status: true,
        createdAt: true,
        description: true,
        // Add aggregated impression data later
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get real analytics data for each app
    const appsWithAnalytics = await Promise.all(
      apps.map(async (app) => {
        const analytics = await getAppAnalytics(app.id);

        return {
          ...app,
          impressions: analytics.impressions,
          clicks: analytics.clicks,
          revenue: analytics.revenue,
          ctr: analytics.ctr,
        };
      }),
    );

    return NextResponse.json({ apps: appsWithAnalytics });
  } catch (error) {
    console.error("Apps fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(appSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { name, description } = validation.data;

    // Generate unique app ID and secret
    const appId = `app_${nanoid(16)}`;
    const appSecret = `secret_${nanoid(32)}`;

    const app = await prisma.app.create({
      data: {
        userId: session.user.id,
        name,
        appId,
        appSecret,
        description: description || null,
      },
      select: {
        id: true,
        name: true,
        appId: true,
        status: true,
        createdAt: true,
        description: true,
      },
    });

    return NextResponse.json(
      {
        message: "App created successfully",
        app: {
          ...app,
          impressions: 0,
          clicks: 0,
          revenue: 0,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("App creation error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

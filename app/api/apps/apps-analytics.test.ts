import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Decimal } from "@prisma/client/runtime/library";
import { getServerSession } from "next-auth";

import { GET } from "@/app/api/apps/route";
import { prisma } from "@/lib/db";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/db", () => ({
  prisma: {
    app: {
      findMany: vi.fn(),
    },
    adImpression: {
      findMany: vi.fn(),
    },
  },
}));

describe("Apps API with Real Analytics", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return apps with real analytics data", async () => {
    // Mock session
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["MODEL_PROVIDER"],
      },
    } as any);

    // Mock apps
    const mockApps = [
      {
        id: "app-1",
        name: "AI Chat App",
        appId: "app_12345",
        status: "ACTIVE",
        createdAt: new Date("2024-01-01"),
        description: "AI-powered chat application",
      },
      {
        id: "app-2",
        name: "Code Assistant",
        appId: "app_67890",
        status: "ACTIVE",
        createdAt: new Date("2024-01-02"),
        description: "AI code completion tool",
      },
    ];

    vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);

    // Mock impressions for each app
    vi.mocked(prisma.adImpression.findMany)
      .mockResolvedValueOnce([
        // App 1 impressions
        {
          clicked: false,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("1.00") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("1.00") },
        },
        {
          clicked: true,
          advertisement: { bidType: "CPC", bidAmount: new Decimal("1.00") },
        },
      ] as any)
      .mockResolvedValueOnce([
        // App 2 impressions (CPM)
        ...Array.from({ length: 2000 }, (_, i) => ({
          clicked: i < 40, // 40 clicks out of 2000 impressions
          advertisement: { bidType: "CPM", bidAmount: new Decimal("3.00") },
        })),
      ] as any);

    const request = new NextRequest("http://localhost:3000/api/apps");
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.apps).toHaveLength(2);

    // Check first app (CPC revenue)
    expect(data.apps[0]).toMatchObject({
      id: "app-1",
      name: "AI Chat App",
      impressions: 3,
      clicks: 2,
      revenue: 0.6, // 2 clicks * $1.00 * 30% revenue share
    });

    // Check second app (CPM revenue)
    expect(data.apps[1]).toMatchObject({
      id: "app-2",
      name: "Code Assistant",
      impressions: 2000,
      clicks: 40,
    });
    expect(data.apps[1].revenue).toBeCloseTo(1.8, 1); // 2000/1000 * $3.00 * 30% revenue share
  });

  it("should return zero analytics for apps with no impressions", async () => {
    // Mock session
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["MODEL_PROVIDER"],
      },
    } as any);

    // Mock app with no impressions
    const mockApps = [
      {
        id: "app-1",
        name: "New App",
        appId: "app_new123",
        status: "ACTIVE",
        createdAt: new Date("2024-01-01"),
        description: "Brand new app",
      },
    ];

    vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);
    vi.mocked(prisma.adImpression.findMany).mockResolvedValue([]);

    const request = new NextRequest("http://localhost:3000/api/apps");
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.apps).toHaveLength(1);
    expect(data.apps[0]).toMatchObject({
      id: "app-1",
      name: "New App",
      impressions: 0,
      clicks: 0,
      revenue: 0,
    });
  });

  it("should calculate mixed CPC and CPM revenue correctly", async () => {
    // Mock session
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["MODEL_PROVIDER"],
      },
    } as any);

    const mockApps = [
      {
        id: "app-1",
        name: "Mixed Revenue App",
        appId: "app_mixed",
        status: "ACTIVE",
        createdAt: new Date("2024-01-01"),
        description: "App with mixed ad types",
      },
    ];

    vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);

    // Mock mixed impressions (CPC and CPM)
    vi.mocked(prisma.adImpression.findMany).mockResolvedValue([
      // CPC impressions
      {
        clicked: true,
        advertisement: { bidType: "CPC", bidAmount: new Decimal("2.00") },
      },
      {
        clicked: false,
        advertisement: { bidType: "CPC", bidAmount: new Decimal("2.00") },
      },
      // CPM impressions (1000 impressions)
      ...Array.from({ length: 1000 }, () => ({
        clicked: false,
        advertisement: { bidType: "CPM", bidAmount: new Decimal("5.00") },
      })),
    ] as any);

    const request = new NextRequest("http://localhost:3000/api/apps");
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.apps[0]).toMatchObject({
      impressions: 1002, // 2 CPC + 1000 CPM
      clicks: 1,
    });
    expect(data.apps[0].revenue).toBeCloseTo(2.1, 1); // (1 click * $2.00 + 1000/1000 * $5.00) * 30% = (2 + 5) * 0.3 = 2.1
  });

  it("should return 401 for unauthorized users", async () => {
    vi.mocked(getServerSession).mockResolvedValue(null);

    const request = new NextRequest("http://localhost:3000/api/apps");
    const response = await GET(request);

    expect(response.status).toBe(401);
  });

  it("should return 401 for users without MODEL_PROVIDER role", async () => {
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["ADVERTISER"],
      },
    } as any);

    const request = new NextRequest("http://localhost:3000/api/apps");
    const response = await GET(request);

    expect(response.status).toBe(401);
  });

  it("should handle database errors gracefully", async () => {
    vi.mocked(getServerSession).mockResolvedValue({
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: ["MODEL_PROVIDER"],
      },
    } as any);

    vi.mocked(prisma.app.findMany).mockRejectedValue(
      new Error("Database error"),
    );

    const request = new NextRequest("http://localhost:3000/api/apps");
    const response = await GET(request);

    expect(response.status).toBe(500);
    expect(await response.json()).toEqual({
      error: "Internal server error",
    });
  });
});

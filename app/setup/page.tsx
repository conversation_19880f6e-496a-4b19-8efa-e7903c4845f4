"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Checkbox } from "@heroui/checkbox";
import { Chip } from "@heroui/chip";
import { Input } from "@heroui/input";
import { Role } from "@prisma/client";

export default function SetupPage() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [name, setName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    // Redirect if user is not authenticated
    if (status === "unauthenticated") {
      router.push("/login");

      return;
    }

    // Redirect if user already has roles assigned
    if (status === "authenticated" && session?.user?.roles?.length > 0) {
      // Redirect based on role count and type
      if (session.user.roles.length === 1) {
        if (session.user.roles.includes("MODEL_PROVIDER")) {
          router.push("/dashboard/model");
        } else if (session.user.roles.includes("ADVERTISER")) {
          router.push("/dashboard/advertiser");
        } else {
          router.push("/dashboard");
        }
      } else if (session.user.roles.length > 1) {
        router.push("/dashboard/role-selection");
      }

      return;
    }

    // Pre-fill name if available from OAuth
    if (session?.user?.name) {
      setName(session.user.name);
    }
  }, [session, status, router]);

  const handleRoleToggle = (role: string) => {
    setSelectedRoles((prev) =>
      prev.includes(role) ? prev.filter((r) => r !== role) : [...prev, role],
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    if (selectedRoles.length === 0) {
      setError("Please select at least one role");
      setIsLoading(false);

      return;
    }

    try {
      // First, update name if provided
      if (name.trim() && name !== session?.user?.name) {
        await fetch("/api/auth/update-profile", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: name.trim(),
          }),
        });
      }

      // Then assign roles
      const response = await fetch("/api/auth/assign-roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roles: selectedRoles,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Update the session with new roles
        await update({ roles: selectedRoles as Role[] });

        // Redirect based on selected roles
        if (selectedRoles.length === 1) {
          if (selectedRoles.includes("MODEL_PROVIDER")) {
            router.push("/dashboard/model");
          } else if (selectedRoles.includes("ADVERTISER")) {
            router.push("/dashboard/advertiser");
          } else {
            router.push("/dashboard");
          }
        } else {
          // Multiple roles - go to role selection dashboard
          router.push("/dashboard/role-selection");
        }
      } else {
        setError(data.error || "Failed to complete setup");
      }
    } catch {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="w-8 h-8 mx-auto border-b-2 rounded-full animate-spin border-primary" />
          <p className="mt-2 text-default-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session || (session.user.roles && session.user.roles.length > 0)) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-[80vh] py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <div className="text-6xl">🚀</div>
          <h1 className="text-2xl font-bold">Welcome to Mindify AiD!</h1>
          <p className="text-default-600">
            Let&apos;s set up your account to get started with the platform.
          </p>
        </CardHeader>
        <CardBody>
          <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
            {error && (
              <div className="p-3 text-sm rounded-lg text-danger bg-danger/10">
                {error}
              </div>
            )}

            <Input
              description="This will be displayed in your profile"
              label="Display Name (Optional)"
              placeholder="Enter your name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />

            <div className="space-y-3">
              <p className="text-sm font-medium text-default-700">
                Select your role(s):
              </p>

              <div className="space-y-3">
                <div className="flex items-start gap-3 p-3 border rounded-lg border-default-200 hover:border-primary-300 transition-colors">
                  <Checkbox
                    isSelected={selectedRoles.includes("MODEL_PROVIDER")}
                    onValueChange={() => handleRoleToggle("MODEL_PROVIDER")}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">Model Provider</span>
                      <Chip color="primary" size="sm" variant="flat">
                        AI Developer
                      </Chip>
                    </div>
                    <p className="text-sm text-default-600">
                      Register your AI applications and earn revenue from ad
                      placements
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 border rounded-lg border-default-200 hover:border-secondary-300 transition-colors">
                  <Checkbox
                    isSelected={selectedRoles.includes("ADVERTISER")}
                    onValueChange={() => handleRoleToggle("ADVERTISER")}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">Advertiser</span>
                      <Chip color="secondary" size="sm" variant="flat">
                        Marketer
                      </Chip>
                    </div>
                    <p className="text-sm text-default-600">
                      Create and manage advertising campaigns across AI
                      applications
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-3 text-xs rounded-lg bg-default-100 text-default-600">
                💡 You can select both roles if you plan to both develop AI
                applications and run advertising campaigns.
              </div>
            </div>

            <Button
              className="w-full"
              color="primary"
              isDisabled={selectedRoles.length === 0}
              isLoading={isLoading}
              type="submit"
            >
              {isLoading ? "Setting up..." : "Complete Setup"}
            </Button>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}

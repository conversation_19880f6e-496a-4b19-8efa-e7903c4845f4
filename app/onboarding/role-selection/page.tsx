"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Checkbox } from "@heroui/checkbox";
import { Chip } from "@heroui/chip";

export default function RoleSelectionPage() {
  const { status, update } = useSession();
  const router = useRouter();
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    // This page is deprecated - redirect to setup
    router.push("/setup");
  }, [router]);

  const handleRoleToggle = (role: string) => {
    setSelectedRoles((prev) =>
      prev.includes(role) ? prev.filter((r) => r !== role) : [...prev, role],
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    if (selectedRoles.length === 0) {
      setError("Please select at least one role");
      setIsLoading(false);

      return;
    }

    try {
      const response = await fetch("/api/auth/assign-roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roles: selectedRoles,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Update the session with new roles
        await update();

        // Redirect to appropriate dashboard
        if (selectedRoles.includes("MODEL_PROVIDER")) {
          router.push("/dashboard/model");
        } else if (selectedRoles.includes("ADVERTISER")) {
          router.push("/dashboard/advertiser");
        } else {
          router.push("/dashboard");
        }
      } else {
        setError(data.error || "Failed to assign roles");
      }
    } catch {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading while checking authentication
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (status === "unauthenticated") {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-[80vh] py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <div className="text-6xl">🎯</div>
          <h1 className="text-2xl font-bold">Choose Your Role</h1>
          <p className="text-default-600">
            Welcome to Mindify AiD Platform! Please select your role(s) to get
            started.
          </p>
        </CardHeader>
        <CardBody>
          <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
            {error && (
              <div className="p-3 text-sm rounded-lg text-danger bg-danger/10">
                {error}
              </div>
            )}

            <div className="space-y-3">
              <p className="text-sm font-medium text-default-700">
                Select your role(s):
              </p>

              <div className="space-y-3">
                <div className="flex items-start gap-3 p-3 border rounded-lg border-default-200 hover:border-primary-300 transition-colors">
                  <Checkbox
                    isSelected={selectedRoles.includes("MODEL_PROVIDER")}
                    onValueChange={() => handleRoleToggle("MODEL_PROVIDER")}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">Model Provider</span>
                      <Chip color="primary" size="sm" variant="flat">
                        AI Developer
                      </Chip>
                    </div>
                    <p className="text-sm text-default-600">
                      Register your AI applications and earn revenue from ad
                      placements
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 border rounded-lg border-default-200 hover:border-primary-300 transition-colors">
                  <Checkbox
                    isSelected={selectedRoles.includes("ADVERTISER")}
                    onValueChange={() => handleRoleToggle("ADVERTISER")}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">Advertiser</span>
                      <Chip color="secondary" size="sm" variant="flat">
                        Marketer
                      </Chip>
                    </div>
                    <p className="text-sm text-default-600">
                      Create and manage advertising campaigns across AI
                      applications
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-3 text-xs rounded-lg bg-default-100 text-default-600">
                💡 You can select both roles if you plan to both develop AI
                applications and run advertising campaigns.
              </div>
            </div>

            <Button
              className="w-full"
              color="primary"
              isLoading={isLoading}
              type="submit"
            >
              {isLoading ? "Setting up your account..." : "Continue"}
            </Button>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}

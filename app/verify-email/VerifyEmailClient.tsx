"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";

export default function VerifyEmailClient() {
  const [status, setStatus] = useState<
    "loading" | "success" | "error" | "expired"
  >("loading");
  const [message, setMessage] = useState("");
  const [isResending, setIsResending] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  useEffect(() => {
    if (!token) {
      setStatus("error");
      setMessage(
        "Invalid verification link. Please check your email for the correct link.",
      );

      return;
    }
    verifyEmail(token);
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await fetch("/api/auth/verify-email", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ token: verificationToken }),
      });
      const data = await response.json();

      if (response.ok) {
        setStatus("success");
        setMessage(data.message || "Email verified successfully!");
        setTimeout(() => router.push("/login?verified=true"), 3000);
      } else {
        if (data.error?.includes("expired")) {
          setStatus("expired");
          setMessage(
            "Your verification link has expired. Please request a new one.",
          );
        } else {
          setStatus("error");
          setMessage(data.error || "Failed to verify email. Please try again.");
        }
      }
    } catch (err) {
      console.error("Verification error:", err);
      setStatus("error");
      setMessage(
        "An error occurred while verifying your email. Please try again.",
      );
    }
  };

  const handleResendVerification = async () => {
    const email = prompt(
      "Please enter your email address to resend verification:",
    );

    if (!email) return;

    setIsResending(true);
    try {
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      const data = await response.json();

      setMessage(
        response.ok
          ? "Verification email sent! Please check your inbox."
          : data.error || "Failed to send verification email.",
      );
    } catch (err) {
      console.error("Resend error:", err);
      setMessage("An error occurred. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case "loading":
        return <div className="text-6xl text-primary animate-spin">⟳</div>;
      case "success":
        return <div className="text-6xl text-success">✓</div>;
      case "error":
      case "expired":
        return <div className="text-6xl text-danger">✗</div>;
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh] py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <div className="flex justify-center">{getStatusIcon()}</div>
          <h1 className="text-2xl font-bold">
            {status === "loading" && "Verifying Email..."}
            {status === "success" && "Email Verified!"}
            {status === "error" && "Verification Failed"}
            {status === "expired" && "Link Expired"}
          </h1>
        </CardHeader>
        <CardBody className="text-center">
          <div
            className={`p-4 rounded-lg mb-4 ${
              status === "success"
                ? "bg-success/10 text-success"
                : status === "error" || status === "expired"
                  ? "bg-danger/10 text-danger"
                  : "bg-default/10 text-default-600"
            }`}
          >
            {message}
          </div>

          {status === "success" && (
            <div className="space-y-4">
              <p className="text-default-600">
                Redirecting to login page in 3 seconds...
              </p>
              <Button
                as={Link}
                className="w-full"
                color="primary"
                href="/login?verified=true"
              >
                Go to Login Now
              </Button>
            </div>
          )}

          {(status === "error" || status === "expired") && (
            <div className="space-y-4">
              <Button
                className="w-full"
                color="primary"
                isLoading={isResending}
                onPress={handleResendVerification}
              >
                {isResending ? "Sending..." : "Resend Verification Email"}
              </Button>
              <div className="text-sm text-center text-default-600">
                Already verified?{" "}
                <Link color="primary" href="/login">
                  Sign in
                </Link>
              </div>
            </div>
          )}

          {status === "loading" && (
            <p className="text-default-600">
              Please wait while we verify your email address...
            </p>
          )}
        </CardBody>
      </Card>
    </div>
  );
}

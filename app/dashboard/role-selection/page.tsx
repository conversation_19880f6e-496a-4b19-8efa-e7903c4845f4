"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { But<PERSON> } from "@heroui/button";
import { Chip } from "@heroui/chip";

export default function RoleSelectionDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");

      return;
    }

    if (session) {
      // Check if user has no roles assigned - redirect to setup
      if (!session.user.roles || session.user.roles.length === 0) {
        router.push("/setup");

        return;
      }

      // If user has only one role, redirect directly to that dashboard
      if (session.user.roles.length === 1) {
        if (session.user.roles.includes("MODEL_PROVIDER")) {
          router.push("/dashboard/model");
        } else if (session.user.roles.includes("ADVERTISER")) {
          router.push("/dashboard/advertiser");
        } else {
          router.push("/setup");
        }

        return;
      }
    }
  }, [session, status, router]);

  const handleRoleSelection = (role: string) => {
    if (role === "MODEL_PROVIDER") {
      router.push("/dashboard/model");
    } else if (role === "ADVERTISER") {
      router.push("/dashboard/advertiser");
    }
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="w-8 h-8 mx-auto border-b-2 rounded-full animate-spin border-primary" />
          <p className="mt-2 text-default-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session || !session.user.roles || session.user.roles.length < 2) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-[80vh] py-8">
      <Card className="w-full max-w-lg">
        <CardHeader className="flex flex-col gap-3 text-center">
          <div className="text-6xl">🎯</div>
          <h1 className="text-2xl font-bold">Choose Your Dashboard</h1>
          <p className="text-default-600">
            You have multiple roles. Please select which dashboard you&apos;d
            like to access.
          </p>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            {session.user.roles.includes("MODEL_PROVIDER") && (
              <Card
                isPressable
                className="cursor-pointer hover:shadow-md transition-shadow border-2 border-transparent hover:border-primary-200"
                onPress={() => handleRoleSelection("MODEL_PROVIDER")}
              >
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">🤖</div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold">
                            Model Provider Dashboard
                          </span>
                          <Chip color="primary" size="sm" variant="flat">
                            AI Developer
                          </Chip>
                        </div>
                        <p className="text-sm text-default-600">
                          Manage your AI applications and track revenue
                        </p>
                      </div>
                    </div>
                    <Button
                      color="primary"
                      size="sm"
                      variant="flat"
                      onPress={() => handleRoleSelection("MODEL_PROVIDER")}
                    >
                      Access
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}

            {session.user.roles.includes("ADVERTISER") && (
              <Card
                isPressable
                className="cursor-pointer hover:shadow-md transition-shadow border-2 border-transparent hover:border-secondary-200"
                onPress={() => handleRoleSelection("ADVERTISER")}
              >
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">📢</div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold">
                            Advertiser Dashboard
                          </span>
                          <Chip color="secondary" size="sm" variant="flat">
                            Marketer
                          </Chip>
                        </div>
                        <p className="text-sm text-default-600">
                          Create and manage advertising campaigns
                        </p>
                      </div>
                    </div>
                    <Button
                      color="secondary"
                      size="sm"
                      variant="flat"
                      onPress={() => handleRoleSelection("ADVERTISER")}
                    >
                      Access
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}

            <div className="pt-4 border-t border-default-200">
              <p className="text-xs text-center text-default-500">
                You can switch between dashboards anytime by visiting this page
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}

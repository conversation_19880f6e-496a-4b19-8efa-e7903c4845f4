"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { getAuthRedirectPath } from "@/lib/auth-routing";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "loading") return;

    const redirectPath = getAuthRedirectPath(session);

    if (redirectPath !== "/dashboard") {
      router.push(redirectPath);
    }
  }, [session, status, router]);

  // Show loading while redirecting
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="w-8 h-8 mx-auto border-b-2 rounded-full animate-spin border-primary" />
          <p className="mt-2 text-default-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show loading while redirecting authenticated users
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <div className="text-center">
        <div className="w-8 h-8 mx-auto border-b-2 rounded-full animate-spin border-primary" />
        <p className="mt-2 text-default-600">Redirecting...</p>
      </div>
    </div>
  );
}

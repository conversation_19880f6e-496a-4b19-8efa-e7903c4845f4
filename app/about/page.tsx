"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import Next<PERSON><PERSON> from "next/link";
import clsx from "clsx";

import { title, subtitle } from "@/components/primitives";

export default function AboutPage() {
  return (
    <div className="w-full px-6 md:px-12 py-16 max-w-screen-xl mx-auto flex flex-col gap-24">
      {/* Hero Section */}
      <section className="relative text-center">
        <div className="absolute inset-0 bg-gradient-to-t from-secondary/20 via-transparent to-primary/20 blur-2xl pointer-events-none" />
        <div className="relative z-10">
          <h1 className={clsx(title({ size: "lg" }), "leading-tight")}>
            About{" "}
            <span className={title({ color: "violet", size: "lg" })}>
              Mindify AI
            </span>
          </h1>
          <p
            className={subtitle({
              class: "mt-6 text-xl text-default-600 max-w-3xl mx-auto",
            })}
          >
            Mindify AI empowers developers and advertisers with intelligent,
            context-aware ad solutions that are accessible, transparent, and
            designed for real-world impact.
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="grid md:grid-cols-2 gap-16 text-left">
        <div>
          <h2 className={title({ size: "md" })}>Our Mission</h2>
          <p
            className={subtitle({
              class: "mt-4 text-default-600 leading-relaxed",
            })}
          >
            To democratize AI monetization by building a free, easy-to-integrate
            contextual advertising platform that rewards model developers fairly
            and delivers advertisers unmatched precision and clarity.
          </p>
        </div>
        <div>
          <h2 className={title({ size: "md" })}>Our Vision</h2>
          <p
            className={subtitle({
              class: "mt-4 text-default-600 leading-relaxed",
            })}
          >
            We envision a thriving ecosystem where AI advancements are
            sustainably funded, end users benefit from highly relevant
            experiences, and the economic value created is shared across the
            entire AI community.
          </p>
        </div>
      </section>

      {/* Call to Action */}
      <section className="text-center">
        <h2 className={title({ size: "md" })}>Join the Mindify Movement</h2>
        <p
          className={subtitle({
            class: "mt-4 text-default-600 max-w-2xl mx-auto",
          })}
        >
          Whether you&apos;re building AI apps or launching campaigns, Mindify
          AI is designed to scale your efforts with intelligence and integrity.
        </p>
        <Button
          as={NextLink}
          className="mt-8 font-semibold px-8 py-4 text-base transition-transform hover:-translate-y-1"
          color="primary"
          href="/register"
          size="lg"
        >
          Get Started Free
        </Button>
      </section>
    </div>
  );
}

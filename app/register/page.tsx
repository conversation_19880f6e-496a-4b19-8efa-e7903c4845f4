"use client";

import { useState } from "react";
import { signIn, getSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Input } from "@heroui/input";
import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";

export default function RegisterPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const router = useRouter();

  const handleResendVerification = async () => {
    setIsResending(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: userEmail }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess("Verification email sent! Please check your inbox.");
      } else {
        setError(data.error || "Failed to send verification email.");
      }
    } catch (error) {
      console.error("Resend error:", error);
      setError("An error occurred. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true);
    setError("");
    setSuccess("");

    try {
      const result = await signIn("google", {
        callbackUrl: "/dashboard",
      });

      if (result?.error) {
        setError("Failed to sign up with Google. Please try again.");
      } else if (result?.ok) {
        // Get the session to determine dashboard routing
        const session = await getSession();

        if (session?.user?.roles && session.user.roles.length > 0) {
          // User already has roles (existing user), redirect based on roles
          if (session.user.roles.length === 1) {
            if (session.user.roles.includes("MODEL_PROVIDER")) {
              router.push("/dashboard/model");
            } else if (session.user.roles.includes("ADVERTISER")) {
              router.push("/dashboard/advertiser");
            } else {
              router.push("/setup");
            }
          } else {
            // Multiple roles - go to role selection dashboard
            router.push("/dashboard/role-selection");
          }
        } else {
          // New user has no roles assigned - redirect to setup
          router.push("/setup");
        }
      }
    } catch {
      setError("An error occurred during Google sign-up. Please try again.");
    } finally {
      setIsGoogleLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");

    // Validation
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);

      return;
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters long");
      setIsLoading(false);

      return;
    }

    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          roles: [], // No roles assigned during registration
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setUserEmail(email);
        setShowEmailVerification(true);
        setSuccess(
          data.message ||
            "Account created successfully! Please check your email to verify your account.",
        );
      } else {
        setError(data.error || "An error occurred");
      }
    } catch {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show email verification screen after successful registration
  if (showEmailVerification) {
    return (
      <div className="flex items-center justify-center min-h-[80vh] py-8">
        <Card className="w-full max-w-md">
          <CardHeader className="flex flex-col gap-3 text-center">
            <div className="text-6xl text-primary">📧</div>
            <h1 className="text-2xl font-bold">Check Your Email</h1>
            <p className="text-default-600">
              We&apos;ve sent a verification link to {userEmail}
            </p>
          </CardHeader>
          <CardBody>
            {error && (
              <div className="p-3 text-sm rounded-lg text-danger bg-danger/10 mb-4">
                {error}
              </div>
            )}

            {success && (
              <div className="p-3 text-sm rounded-lg text-success bg-success/10 mb-4">
                {success}
              </div>
            )}

            <div className="space-y-4">
              <p className="text-sm text-default-600 text-center">
                Please click the verification link in your email to activate
                your account. If you don&apos;t see the email, check your spam
                folder.
              </p>

              <Button
                className="w-full"
                color="primary"
                isLoading={isResending}
                variant="bordered"
                onPress={handleResendVerification}
              >
                {isResending ? "Sending..." : "Resend Verification Email"}
              </Button>

              <div className="text-sm text-center text-default-600">
                Already verified?{" "}
                <Link color="primary" href="/login">
                  Sign in
                </Link>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-[80vh] py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <h1 className="text-2xl font-bold">Create Account</h1>
          <p className="text-default-600">Join Mindify AiD Platform today</p>
        </CardHeader>
        <CardBody>
          <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
            {error && (
              <div className="p-3 text-sm rounded-lg text-danger bg-danger/10">
                {error}
              </div>
            )}

            {success && (
              <div className="p-3 text-sm rounded-lg text-success bg-success/10">
                {success}
              </div>
            )}

            <Input
              required
              isDisabled={isLoading}
              label="Email"
              placeholder="Enter your email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />

            <Input
              required
              description="Must be at least 8 characters long"
              isDisabled={isLoading}
              label="Password"
              placeholder="Enter your password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />

            <Input
              required
              isDisabled={isLoading}
              label="Confirm Password"
              placeholder="Confirm your password"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />

            <Button
              className="w-full"
              color="primary"
              isLoading={isLoading}
              type="submit"
            >
              {isLoading ? "Creating Account..." : "Create Account"}
            </Button>

            <div className="flex items-center gap-4 my-4">
              <div className="flex-1 border-t border-default-200" />
              <span className="text-sm text-default-500">or</span>
              <div className="flex-1 border-t border-default-200" />
            </div>

            <Button
              className="w-full"
              color="default"
              isLoading={isGoogleLoading}
              startContent={
                !isGoogleLoading && (
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                )
              }
              variant="bordered"
              onPress={handleGoogleSignUp}
            >
              {isGoogleLoading ? "Creating Account..." : "Sign up with Google"}
            </Button>

            <div className="text-sm text-center text-default-600">
              Already have an account?{" "}
              <Link color="primary" href="/login">
                Sign in
              </Link>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}

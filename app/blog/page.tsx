import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from "@heroui/card";
import Link from "next/link";

import { title } from "@/components/primitives";
import { getAllBlogPosts } from "@/lib/mdx";

export default function BlogPage() {
  const posts = getAllBlogPosts();

  return (
    <div className="max-w-4xl mx-auto px-4 py-16 space-y-6">
      <h1 className={title()}>Blog</h1>
      <p className="text-lg text-default-500">
        Insights, updates, and journey from the Mindify AI team.
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        {posts.map((post) => (
          <Link key={post.slug} href={`/blog/${post.slug}`}>
            <Card
              isHoverable
              isPressable
              className="w-full"
              radius="lg"
              shadow="md"
            >
              <CardHeader>
                <h2 className="text-xl font-semibold line-clamp-2">
                  {post.title}
                </h2>
              </CardHeader>

              <CardBody>
                <p className="text-default-600 line-clamp-3">
                  {post.description}
                </p>
              </CardBody>

              <CardFooter>
                <time className="text-sm text-default-400">
                  {new Date(post.date).toLocaleDateString()}
                </time>
              </CardFooter>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}

// app/blog/[slug]/page.tsx
import { notFound } from "next/navigation";
import Image from "next/image";
// Import the new processing libraries
import { unified } from "unified";
import remarkParse from "remark-parse";
import remarkMdx from "remark-mdx";
import remarkRehype from "remark-rehype";
import rehypeHighlight from "rehype-highlight"; // For syntax highlighting
import rehypeStringify from "rehype-stringify";

import { getPostBySlug, getPostSlugs } from "@/lib/blog";

// Type definitions
type Params = { slug: string };
type PageProps = { params: Promise<Params> };

// Revalidate the page every hour
export const revalidate = 3600;

// Generate static paths (no changes here)
export function generateStaticParams(): Params[] {
  const slugs = getPostSlugs();

  return slugs.map((slug) => ({ slug }));
}

// Generate dynamic metadata (no changes here)
export async function generateMetadata({ params }: PageProps) {
  const { slug } = await params;
  const post = await getPostBySlug(slug);

  if (!post) return { title: "Post Not Found" };

  return {
    title: post.frontmatter.title,
    description: post.frontmatter.description,
  };
}

// The main blog post page component
export default async function BlogPostPage({ params }: PageProps) {
  const { slug } = await params;
  const post = await getPostBySlug(slug);

  if (!post) {
    notFound();
  }

  const { frontmatter, content } = post;

  // Manually convert the MDX content to an HTML string
  const processedContent = await unified()
    .use(remarkParse)
    .use(remarkMdx)
    .use(remarkRehype)
    .use(rehypeHighlight) // Apply syntax highlighting
    .use(rehypeStringify)
    .process(content);
  const contentHtml = processedContent.toString();

  const formattedDate = new Intl.DateTimeFormat("en-US", {
    dateStyle: "long",
  }).format(new Date(frontmatter.date));

  return (
    <article className="max-w-3xl mx-auto py-16 px-6 sm:px-8 lg:px-0">
      {/* Feature Image */}
      {frontmatter.image && (
        <div className="mb-8 w-full h-auto aspect-video relative overflow-hidden rounded-lg shadow-lg">
          <Image
            fill
            priority
            alt={frontmatter.title}
            className="object-cover"
            src={frontmatter.image}
          />
        </div>
      )}

      {/* Header */}
      <header className="mb-10">
        <h1 className="text-4xl md:text-5xl font-extrabold text-gray-900 dark:text-gray-100 mb-4">
          {frontmatter.title}
        </h1>
        <div className="flex flex-wrap text-sm text-gray-500 dark:text-gray-400 space-x-4">
          <time dateTime={frontmatter.date}>{formattedDate}</time>
          <span className="text-gray-300 dark:text-gray-600">•</span>
          <span>
            By <strong>{frontmatter.author}</strong>
          </span>
        </div>
      </header>

      {/* Content rendered from an HTML string */}
      <div
        dangerouslySetInnerHTML={{ __html: contentHtml }}
        className="prose prose-lg dark:prose-invert max-w-none text-left" // <-- Add text-right
      />
    </article>
  );
}

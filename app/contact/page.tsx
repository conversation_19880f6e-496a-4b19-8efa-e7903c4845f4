"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { Textarea } from "@heroui/input";
import NextLink from "next/link";
import clsx from "clsx";

import { title, subtitle } from "@/components/primitives";

export default function ContactPage() {
  return (
    <div className="flex flex-col gap-16 py-16 px-4 md:px-0 max-w-screen-md mx-auto">
      {/* Header */}
      <section className="relative text-center">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10 blur-xl pointer-events-none" />
        <div className="relative z-10">
          <h1 className={clsx(title({ size: "lg" }), "leading-snug")}>
            Get in{" "}
            <span className={title({ color: "violet", size: "lg" })}>
              Touch
            </span>
          </h1>
          <p className={subtitle({ class: "mt-4 text-default-600 text-lg" })}>
            Whether you have questions, feedback, or partnership inquiries, our
            team is here to help. Drop us a message below or reach out directly.
          </p>
        </div>
      </section>

      {/* Contact Form */}
      <section>
        <form className="grid gap-6">
          <div>
            <Input className="w-full" placeholder="Your Name" size="lg" />
          </div>
          <div>
            <Input
              className="w-full"
              placeholder="Your Email"
              size="lg"
              type="email"
            />
          </div>
          <div>
            <Textarea className="w-full" placeholder="Your Message" rows={6} />
          </div>
          <div className="text-center">
            <Button
              className="font-semibold shadow-lg transition-transform hover:-translate-y-1"
              color="primary"
              size="lg"
              type="submit"
            >
              Send Message
            </Button>
          </div>
        </form>
      </section>

      {/* Contact Details */}
      <section className="grid gap-4 text-center text-default-600">
        <p>
          Email:{" "}
          <a className="text-primary" href="mailto:<EMAIL>">
            <EMAIL>
          </a>
        </p>
        <p>Address: 131 Continental Dr Suite 305 Newark, DE, 19713 US</p>
      </section>

      {/* Back to Home */}
      <section className="text-center">
        <NextLink className="text-primary hover:underline" href="/">
          ← Back to Home
        </NextLink>
      </section>
    </div>
  );
}

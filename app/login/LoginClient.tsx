"use client";

import { useState, useEffect } from "react";
import { signIn, getSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Input } from "@heroui/input";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import { Checkbox } from "@heroui/checkbox";

import { getAuthRedirectPath } from "@/lib/auth-routing";

export default function LoginClient() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [showResendVerification, setShowResendVerification] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get("verified") === "true") {
      setSuccess("Email verified successfully! You can now sign in.");
    }
  }, [searchParams]);

  const handleResendVerification = async () => {
    setIsResending(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      const data = await response.json();

      if (response.ok) {
        setSuccess("Verification email sent! Please check your inbox.");
        setShowResendVerification(false);
      } else {
        setError(data.error || "Failed to send verification email.");
      }
    } catch (err) {
      console.error("Resend error:", err);
      setError("An error occurred. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    setError("");
    setSuccess("");

    try {
      const result = await signIn("google", {
        callbackUrl: "/dashboard",
      });

      if (result?.error) {
        setError("Failed to sign in with Google. Please try again.");
      } else if (result?.ok) {
        // Get the session to determine dashboard routing
        const session = await getSession();
        const redirectPath = getAuthRedirectPath(session);

        router.push(redirectPath);
      }
    } catch {
      setError("An error occurred during Google sign-in. Please try again.");
    } finally {
      setIsGoogleLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");
    setShowResendVerification(false);

    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        if (result.error === "EMAIL_NOT_VERIFIED") {
          setError("Please verify your email address before signing in.");
          setShowResendVerification(true);
        } else {
          setError("Invalid email or password");
        }
      } else {
        const session = await getSession();
        const redirectPath = getAuthRedirectPath(session);

        router.push(redirectPath);
      }
    } catch {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh]">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <h1 className="text-2xl font-bold">Sign In</h1>
          <p className="text-default-600">
            Welcome back to Mindify AiD Platform
          </p>
        </CardHeader>
        <CardBody>
          <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
            {error && (
              <div className="p-3 text-sm rounded-lg text-danger bg-danger/10">
                {error}
              </div>
            )}
            {success && (
              <div className="p-3 text-sm rounded-lg text-success bg-success/10">
                {success}
              </div>
            )}

            <Input
              required
              isDisabled={isLoading}
              label="Email"
              placeholder="Enter your email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />

            <Input
              required
              isDisabled={isLoading}
              label="Password"
              placeholder="Enter your password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />

            <div className="flex items-center justify-between">
              <Checkbox
                isSelected={rememberMe}
                size="sm"
                onValueChange={setRememberMe}
              >
                Remember me
              </Checkbox>
              <Link href="/forgot-password" size="sm">
                Forgot password?
              </Link>
            </div>

            <Button
              className="w-full"
              color="primary"
              isLoading={isLoading}
              type="submit"
            >
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>

            <div className="flex items-center gap-4 my-4">
              <div className="flex-1 border-t border-default-200" />
              <span className="text-sm text-default-500">or</span>
              <div className="flex-1 border-t border-default-200" />
            </div>

            <Button
              className="w-full"
              color="default"
              isLoading={isGoogleLoading}
              startContent={
                !isGoogleLoading && (
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                )
              }
              variant="bordered"
              onPress={handleGoogleSignIn}
            >
              {isGoogleLoading ? "Signing in..." : "Sign in with Google"}
            </Button>

            {showResendVerification && (
              <Button
                className="w-full"
                color="secondary"
                isLoading={isResending}
                variant="bordered"
                onPress={handleResendVerification}
              >
                {isResending ? "Sending..." : "Resend Verification Email"}
              </Button>
            )}

            <div className="text-sm text-center text-default-600">
              Don&apos;t have an account?{" "}
              <Link color="primary" href="/register">
                Sign up
              </Link>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}

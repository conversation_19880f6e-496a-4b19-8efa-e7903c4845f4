import { describe, it, expect, vi, beforeEach } from "vitest";
import { AppStatus } from "@prisma/client";
import { AppService } from "./app";
import { prisma } from "@/lib/db";
import * as analytics from "@/lib/analytics";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
    },
    app: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
  },
}));

vi.mock("@/lib/analytics", () => ({
  getAppAnalytics: vi.fn(),
}));

vi.mock("nanoid", () => ({
  nanoid: vi.fn(),
}));

describe("AppService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(analytics.getAppAnalytics).mockResolvedValue({
      impressions: 100,
      clicks: 10,
      revenue: 50.0,
      ctr: 10.0,
    });
  });

  describe("createApp", () => {
    it("should create app successfully", async () => {
      const mockCreateData = {
        userId: "user123",
        name: "Test App",
        description: "Test Description",
      };

      const mockUser = {
        id: "user123",
        roles: ["MODEL_PROVIDER"],
      };

      const mockCreatedApp = {
        id: "app123",
        name: "Test App",
        appId: "app_1234567890123456",
        appSecret: "secret_12345678901234567890123456789012",
        description: "Test Description",
        status: AppStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(prisma.app.create).mockResolvedValue(mockCreatedApp as any);

      const result = await AppService.createApp(mockCreateData);

      expect(result.success).toBe(true);
      expect(result.data?.app.name).toBe("Test App");
      expect(result.data?.app.impressions).toBe(0);
      expect(result.data?.app.clicks).toBe(0);
    });

    it("should handle user not found", async () => {
      const mockCreateData = {
        userId: "nonexistent",
        name: "Test App",
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await AppService.createApp(mockCreateData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("User not found");
    });

    it("should handle user without MODEL_PROVIDER role", async () => {
      const mockCreateData = {
        userId: "user123",
        name: "Test App",
      };

      const mockUser = {
        id: "user123",
        roles: ["ADVERTISER"],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await AppService.createApp(mockCreateData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("User must have MODEL_PROVIDER role to create apps");
    });
  });

  describe("getAppById", () => {
    it("should get app successfully", async () => {
      const mockApp = {
        id: "app123",
        name: "Test App",
        appId: "app_1234567890123456",
        description: "Test Description",
        status: AppStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const result = await AppService.getAppById("app123");

      expect(result.success).toBe(true);
      expect(result.data?.app.name).toBe("Test App");
      expect(result.data?.app.impressions).toBe(100);
      expect(result.data?.app.appSecret).toBeUndefined();
    });

    it("should get app with secret when requested", async () => {
      const mockApp = {
        id: "app123",
        name: "Test App",
        appId: "app_1234567890123456",
        appSecret: "secret_12345678901234567890123456789012",
        description: "Test Description",
        status: AppStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const result = await AppService.getAppById("app123", true);

      expect(result.success).toBe(true);
      expect(result.data?.app.appSecret).toBe("secret_12345678901234567890123456789012");
    });

    it("should handle app not found", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue(null);

      const result = await AppService.getAppById("nonexistent");

      expect(result.success).toBe(false);
      expect(result.error).toBe("App not found");
    });
  });

  describe("getUserApps", () => {
    it("should get user apps successfully", async () => {
      const mockApps = [
        {
          id: "app1",
          name: "App 1",
          appId: "app_1111111111111111",
          description: "Description 1",
          status: AppStatus.ACTIVE,
          userId: "user123",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "app2",
          name: "App 2",
          appId: "app_2222222222222222",
          description: "Description 2",
          status: AppStatus.ACTIVE,
          userId: "user123",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);

      const result = await AppService.getUserApps("user123");

      expect(result.success).toBe(true);
      expect(result.data?.apps).toHaveLength(2);
      expect(result.data?.apps[0].impressions).toBe(100);
    });
  });

  describe("updateApp", () => {
    it("should update app successfully", async () => {
      const mockUpdatedApp = {
        id: "app123",
        name: "Updated App",
        appId: "app_1234567890123456",
        description: "Updated Description",
        status: AppStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.app.update).mockResolvedValue(mockUpdatedApp as any);

      const result = await AppService.updateApp("app123", {
        name: "Updated App",
        description: "Updated Description",
      });

      expect(result.success).toBe(true);
      expect(result.data?.app.name).toBe("Updated App");
      expect(result.data?.app.description).toBe("Updated Description");
    });

    it("should update app with user restriction", async () => {
      const mockUpdatedApp = {
        id: "app123",
        name: "Updated App",
        appId: "app_1234567890123456",
        description: "Updated Description",
        status: AppStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.app.update).mockResolvedValue(mockUpdatedApp as any);

      const result = await AppService.updateApp("app123", {
        name: "Updated App",
      }, "user123");

      expect(result.success).toBe(true);
      expect(prisma.app.update).toHaveBeenCalledWith({
        where: { id: "app123", userId: "user123" },
        data: { name: "Updated App" },
        select: expect.any(Object),
      });
    });
  });

  describe("verifyAppCredentials", () => {
    it("should verify credentials successfully", async () => {
      const mockApp = {
        id: "app123",
        name: "Test App",
        appSecret: "secret_12345678901234567890123456789012",
        status: AppStatus.ACTIVE,
        userId: "user123",
      };

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const result = await AppService.verifyAppCredentials(
        "app_1234567890123456",
        "secret_12345678901234567890123456789012"
      );

      expect(result.success).toBe(true);
      expect(result.data?.app.id).toBe("app123");
    });

    it("should handle invalid app ID", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue(null);

      const result = await AppService.verifyAppCredentials(
        "invalid_app_id",
        "secret_12345678901234567890123456789012"
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid app credentials");
    });

    it("should handle invalid app secret", async () => {
      const mockApp = {
        id: "app123",
        name: "Test App",
        appSecret: "secret_12345678901234567890123456789012",
        status: AppStatus.ACTIVE,
        userId: "user123",
      };

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const result = await AppService.verifyAppCredentials(
        "app_1234567890123456",
        "wrong_secret"
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid app credentials");
    });

    it("should handle inactive app", async () => {
      const mockApp = {
        id: "app123",
        name: "Test App",
        appSecret: "secret_12345678901234567890123456789012",
        status: AppStatus.SUSPENDED,
        userId: "user123",
      };

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const result = await AppService.verifyAppCredentials(
        "app_1234567890123456",
        "secret_12345678901234567890123456789012"
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe("App is not active");
    });
  });

  describe("regenerateAppSecret", () => {
    it("should regenerate app secret successfully", async () => {
      const mockUpdatedApp = {
        id: "app123",
        name: "Test App",
        appId: "app_1234567890123456",
        appSecret: "secret_new32characterstring1234567890",
        description: "Test Description",
        status: AppStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.app.update).mockResolvedValue(mockUpdatedApp as any);

      const result = await AppService.regenerateAppSecret("app123");

      expect(result.success).toBe(true);
      expect(result.data?.newSecret).toBeDefined();
      expect(result.data?.app.appSecret).toBeDefined();
    });
  });

  describe("deleteApp", () => {
    it("should delete app successfully", async () => {
      vi.mocked(prisma.app.delete).mockResolvedValue({} as any);

      const result = await AppService.deleteApp("app123");

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe("App deleted successfully");
    });

    it("should delete app with user restriction", async () => {
      vi.mocked(prisma.app.delete).mockResolvedValue({} as any);

      const result = await AppService.deleteApp("app123", "user123");

      expect(result.success).toBe(true);
      expect(prisma.app.delete).toHaveBeenCalledWith({
        where: { id: "app123", userId: "user123" },
      });
    });
  });

  describe("getAppAnalytics", () => {
    it("should get app analytics successfully", async () => {
      const mockApp = {
        id: "app123",
        name: "Test App",
      };

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);

      const result = await AppService.getAppAnalytics("app123");

      expect(result.success).toBe(true);
      expect(result.data?.analytics.impressions).toBe(100);
      expect(result.data?.analytics.clicks).toBe(10);
    });

    it("should handle app not found for analytics", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue(null);

      const result = await AppService.getAppAnalytics("nonexistent");

      expect(result.success).toBe(false);
      expect(result.error).toBe("App not found or access denied");
    });
  });

  describe("getAppStats", () => {
    it("should get app statistics successfully", async () => {
      vi.mocked(prisma.app.count)
        .mockResolvedValueOnce(100) // total apps
        .mockResolvedValueOnce(80)  // active apps
        .mockResolvedValueOnce(20); // suspended apps

      const result = await AppService.getAppStats();

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        totalApps: 100,
        activeApps: 80,
        suspendedApps: 20,
      });
    });
  });
});

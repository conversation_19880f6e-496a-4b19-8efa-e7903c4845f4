import { prisma } from "@/lib/db";
import {
  sendEmail,
  sendVerificationEmail,
  sendWelcomeEmail,
  generateVerificationToken,
  getVerificationExpiry,
  EmailData,
} from "@/lib/email";

export interface EmailServiceResult {
  success: boolean;
  error?: string;
  data?: any;
}

export interface CreateVerificationTokenData {
  userId: string;
  email: string;
  name?: string;
}

export interface ResendVerificationData {
  email: string;
}

/**
 * Email Service - Business logic layer for email operations
 * Handles email verification, welcome emails, and general email sending
 */
export class EmailService {
  /**
   * Send a generic email
   */
  static async sendGenericEmail(
    emailData: EmailData,
  ): Promise<EmailServiceResult> {
    try {
      const result = await sendEmail(emailData);

      return {
        success: result.success,
        error: result.error,
        data: { provider: result.provider },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to send email",
      };
    }
  }

  /**
   * Create and store email verification token for a user
   */
  static async createVerificationToken(
    data: CreateVerificationTokenData,
  ): Promise<EmailServiceResult> {
    try {
      const { userId, email, name } = data;

      // Generate verification token and expiry
      const verificationToken = generateVerificationToken();
      const verificationExpires = getVerificationExpiry();

      // Update user with verification token
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          emailVerificationToken: verificationToken,
          emailVerificationExpires: verificationExpires,
        },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerificationToken: true,
          emailVerificationExpires: true,
        },
      });

      // Send verification email
      const emailResult = await sendVerificationEmail({
        email,
        token: verificationToken,
        name: name || updatedUser.name || undefined,
      });

      return {
        success: emailResult.success,
        error: emailResult.error,
        data: {
          userId: updatedUser.id,
          token: verificationToken,
          expires: verificationExpires,
          emailSent: emailResult.success,
        },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to create verification token",
      };
    }
  }

  /**
   * Verify email using token
   */
  static async verifyEmail(token: string): Promise<EmailServiceResult> {
    try {
      // Find user with matching token
      const user = await prisma.user.findFirst({
        where: {
          emailVerificationToken: token,
          emailVerificationExpires: {
            gt: new Date(),
          },
        },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "Invalid or expired verification token",
        };
      }

      if (user.emailVerified) {
        return {
          success: false,
          error: "Email is already verified",
        };
      }

      // Update user as verified and clear verification fields
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerified: new Date(),
          emailVerificationToken: null,
          emailVerificationExpires: null,
        },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true,
        },
      });

      // Send welcome email
      const welcomeEmailResult = await sendWelcomeEmail(
        updatedUser.email,
        updatedUser.name || undefined,
      );

      return {
        success: true,
        data: {
          user: updatedUser,
          welcomeEmailSent: welcomeEmailResult.success,
        },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to verify email",
      };
    }
  }

  /**
   * Resend verification email
   */
  static async resendVerificationEmail(
    data: ResendVerificationData,
  ): Promise<EmailServiceResult> {
    try {
      const { email } = data;

      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      if (user.emailVerified) {
        return {
          success: false,
          error: "Email is already verified",
        };
      }

      // Create new verification token
      return await this.createVerificationToken({
        userId: user.id,
        email: user.email,
        name: user.name || undefined,
      });
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to resend verification email",
      };
    }
  }

  /**
   * Send welcome email to user
   */
  static async sendWelcomeEmailToUser(
    email: string,
    name?: string,
  ): Promise<EmailServiceResult> {
    try {
      const result = await sendWelcomeEmail(email, name);

      return {
        success: result.success,
        error: result.error,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to send welcome email",
      };
    }
  }

  /**
   * Check if user's email is verified
   */
  static async isEmailVerified(userId: string): Promise<EmailServiceResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          emailVerified: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      return {
        success: true,
        data: {
          isVerified: !!user.emailVerified,
          verifiedAt: user.emailVerified,
        },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to check email verification status",
      };
    }
  }

  /**
   * Get verification token info for a user
   */
  static async getVerificationTokenInfo(
    userId: string,
  ): Promise<EmailServiceResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          emailVerificationToken: true,
          emailVerificationExpires: true,
          emailVerified: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      return {
        success: true,
        data: {
          hasToken: !!user.emailVerificationToken,
          expires: user.emailVerificationExpires,
          isExpired: user.emailVerificationExpires
            ? new Date() > user.emailVerificationExpires
            : false,
          isVerified: !!user.emailVerified,
        },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get verification token info",
      };
    }
  }
}

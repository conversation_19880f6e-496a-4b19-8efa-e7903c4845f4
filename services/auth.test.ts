import { describe, it, expect, vi, beforeEach } from "vitest";
import { Role } from "@prisma/client";
import { AuthService } from "./auth";
import { prisma } from "@/lib/db";
import * as auth from "@/lib/auth";
import * as validation from "@/lib/validation";
import { UserService } from "./user";
import { EmailService } from "./email";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
  },
}));

vi.mock("@/lib/auth", () => ({
  hashPassword: vi.fn(),
  verifyPassword: vi.fn(),
}));

vi.mock("@/lib/validation", () => ({
  sanitizeEmail: vi.fn(),
}));

vi.mock("./user", () => ({
  UserService: {
    createUser: vi.fn(),
    assignRoles: vi.fn(),
    hasRole: vi.fn(),
  },
}));

vi.mock("./email", () => ({
  EmailService: {
    createVerificationToken: vi.fn(),
    verifyEmail: vi.fn(),
    resendVerificationEmail: vi.fn(),
  },
}));

describe("AuthService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(validation.sanitizeEmail).mockImplementation((email) => email.toLowerCase());
  });

  describe("registerUser", () => {
    it("should register user successfully", async () => {
      const mockRegisterData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User",
        roles: [Role.MODEL_PROVIDER],
      };

      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        roles: [Role.MODEL_PROVIDER],
        emailVerified: null,
        createdAt: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
      vi.mocked(UserService.createUser).mockResolvedValue({
        success: true,
        data: { user: mockUser },
      });
      vi.mocked(EmailService.createVerificationToken).mockResolvedValue({
        success: true,
        data: { emailSent: true },
      });

      const result = await AuthService.registerUser(mockRegisterData);

      expect(result.success).toBe(true);
      expect(result.data?.user.email).toBe("<EMAIL>");
      expect(result.data?.emailSent).toBe(true);
    });

    it("should handle existing user", async () => {
      const mockRegisterData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User",
        roles: [Role.MODEL_PROVIDER],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue({ id: "existing" } as any);

      const result = await AuthService.registerUser(mockRegisterData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("User with this email already exists, please sign in.");
    });

    it("should handle invalid roles", async () => {
      const mockRegisterData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User",
        roles: [],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await AuthService.registerUser(mockRegisterData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("At least one role is required");
    });

    it("should handle too many roles", async () => {
      const mockRegisterData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User",
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER, Role.MODEL_PROVIDER] as any,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await AuthService.registerUser(mockRegisterData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Maximum two roles allowed");
    });
  });

  describe("loginUser", () => {
    it("should login user successfully", async () => {
      const mockCredentials = {
        email: "<EMAIL>",
        password: "password123",
      };

      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        passwordHash: "hashed-password",
        roles: [Role.MODEL_PROVIDER],
        emailVerified: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(auth.verifyPassword).mockResolvedValue(true);

      const result = await AuthService.loginUser(mockCredentials);

      expect(result.success).toBe(true);
      expect(result.data?.user.email).toBe("<EMAIL>");
      expect(result.data?.user.roles).toEqual([Role.MODEL_PROVIDER]);
    });

    it("should handle user not found", async () => {
      const mockCredentials = {
        email: "<EMAIL>",
        password: "password123",
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await AuthService.loginUser(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid email or password");
    });

    it("should handle OAuth-only user", async () => {
      const mockCredentials = {
        email: "<EMAIL>",
        password: "password123",
      };

      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "OAuth User",
        image: null,
        passwordHash: null, // OAuth user
        roles: [Role.MODEL_PROVIDER],
        emailVerified: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await AuthService.loginUser(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toBe("This account was created with a social provider. Please sign in using that method.");
    });

    it("should handle invalid password", async () => {
      const mockCredentials = {
        email: "<EMAIL>",
        password: "wrongpassword",
      };

      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        passwordHash: "hashed-password",
        roles: [Role.MODEL_PROVIDER],
        emailVerified: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(auth.verifyPassword).mockResolvedValue(false);

      const result = await AuthService.loginUser(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid email or password");
    });

    it("should handle unverified email", async () => {
      const mockCredentials = {
        email: "<EMAIL>",
        password: "password123",
      };

      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Unverified User",
        image: null,
        passwordHash: "hashed-password",
        roles: [Role.MODEL_PROVIDER],
        emailVerified: null,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(auth.verifyPassword).mockResolvedValue(true);

      const result = await AuthService.loginUser(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toBe("EMAIL_NOT_VERIFIED");
      expect(result.data?.userId).toBe("user123");
    });
  });

  describe("changePassword", () => {
    it("should change password successfully", async () => {
      const mockData = {
        userId: "user123",
        currentPassword: "oldpassword",
        newPassword: "newpassword",
      };

      const mockUser = {
        id: "user123",
        passwordHash: "old-hashed-password",
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(auth.verifyPassword).mockResolvedValue(true);
      vi.mocked(auth.hashPassword).mockResolvedValue("new-hashed-password");
      vi.mocked(prisma.user.update).mockResolvedValue({} as any);

      const result = await AuthService.changePassword(mockData);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe("Password changed successfully");
    });

    it("should handle user not found", async () => {
      const mockData = {
        userId: "nonexistent",
        currentPassword: "oldpassword",
        newPassword: "newpassword",
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await AuthService.changePassword(mockData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("User not found");
    });

    it("should handle OAuth user without password", async () => {
      const mockData = {
        userId: "user123",
        currentPassword: "oldpassword",
        newPassword: "newpassword",
      };

      const mockUser = {
        id: "user123",
        passwordHash: null,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await AuthService.changePassword(mockData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("This account doesn't have a password. It was created with a social provider.");
    });

    it("should handle incorrect current password", async () => {
      const mockData = {
        userId: "user123",
        currentPassword: "wrongpassword",
        newPassword: "newpassword",
      };

      const mockUser = {
        id: "user123",
        passwordHash: "hashed-password",
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(auth.verifyPassword).mockResolvedValue(false);

      const result = await AuthService.changePassword(mockData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Current password is incorrect");
    });
  });

  describe("getRedirectPath", () => {
    it("should redirect to verification for unverified email", () => {
      const user = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER],
        emailVerified: null,
      };

      const path = AuthService.getRedirectPath(user);
      expect(path).toBe("/verify-email");
    });

    it("should redirect to onboarding for users without roles", () => {
      const user = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [],
        emailVerified: new Date(),
      };

      const path = AuthService.getRedirectPath(user);
      expect(path).toBe("/onboarding");
    });

    it("should redirect to provider dashboard for model provider", () => {
      const user = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER],
        emailVerified: new Date(),
      };

      const path = AuthService.getRedirectPath(user);
      expect(path).toBe("/dashboard/provider");
    });

    it("should redirect to advertiser dashboard for advertiser", () => {
      const user = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.ADVERTISER],
        emailVerified: new Date(),
      };

      const path = AuthService.getRedirectPath(user);
      expect(path).toBe("/dashboard/advertiser");
    });

    it("should redirect to general dashboard for multiple roles", () => {
      const user = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
        emailVerified: new Date(),
      };

      const path = AuthService.getRedirectPath(user);
      expect(path).toBe("/dashboard");
    });
  });

  describe("assignUserRoles", () => {
    it("should assign roles successfully", async () => {
      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        roles: [Role.MODEL_PROVIDER],
      };

      vi.mocked(UserService.assignRoles).mockResolvedValue({
        success: true,
        data: { user: mockUser },
      });

      const result = await AuthService.assignUserRoles("user123", [Role.MODEL_PROVIDER]);

      expect(result.success).toBe(true);
      expect(result.data?.user.roles).toEqual([Role.MODEL_PROVIDER]);
    });

    it("should handle invalid roles", async () => {
      const result = await AuthService.assignUserRoles("user123", []);

      expect(result.success).toBe(false);
      expect(result.error).toBe("At least one role is required");
    });
  });

  describe("verifyUserEmail", () => {
    it("should verify email successfully", async () => {
      const mockResult = {
        success: true,
        data: {
          user: { id: "user123", email: "<EMAIL>" },
          welcomeEmailSent: true,
        },
      };

      vi.mocked(EmailService.verifyEmail).mockResolvedValue(mockResult);

      const result = await AuthService.verifyUserEmail("valid-token");

      expect(result.success).toBe(true);
      expect(result.data?.welcomeEmailSent).toBe(true);
    });

    it("should handle invalid token", async () => {
      vi.mocked(EmailService.verifyEmail).mockResolvedValue({
        success: false,
        error: "Invalid token",
      });

      const result = await AuthService.verifyUserEmail("invalid-token");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid token");
    });
  });
});

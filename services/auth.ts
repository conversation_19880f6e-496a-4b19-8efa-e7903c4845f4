import { Role } from "@prisma/client";
import { prisma } from "@/lib/db";
import { hashPassword, verifyPassword } from "@/lib/auth";
import { sanitizeEmail } from "@/lib/validation";
import { UserService } from "./user";
import { EmailService } from "./email";

export interface AuthServiceResult {
  success: boolean;
  error?: string;
  data?: any;
}

export interface RegisterUserData {
  email: string;
  password: string;
  name?: string;
  roles: Role[];
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface ChangePasswordData {
  userId: string;
  currentPassword: string;
  newPassword: string;
}

export interface ResetPasswordData {
  email: string;
  token: string;
  newPassword: string;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string | null;
  image: string | null;
  roles: Role[];
  emailVerified: Date | null;
}

/**
 * Auth Service - Business logic layer for authentication operations
 * Handles registration, login, password management, and role-based authentication flows
 */
export class AuthService {
  /**
   * Register a new user with email verification
   */
  static async registerUser(data: RegisterUserData): Promise<AuthServiceResult> {
    try {
      const { email, password, name, roles } = data;

      // Sanitize email
      const sanitizedEmail = sanitizeEmail(email);

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: sanitizedEmail },
      });

      if (existingUser) {
        return {
          success: false,
          error: "User with this email already exists, please sign in.",
        };
      }

      // Validate roles
      if (!roles || roles.length === 0) {
        return {
          success: false,
          error: "At least one role is required",
        };
      }

      if (roles.length > 2) {
        return {
          success: false,
          error: "Maximum two roles allowed",
        };
      }

      // Create user using UserService
      const userResult = await UserService.createUser({
        email: sanitizedEmail,
        password,
        name,
        roles,
        emailVerified: false,
      });

      if (!userResult.success) {
        return userResult;
      }

      const user = userResult.data.user;

      // Create verification token and send email
      const emailResult = await EmailService.createVerificationToken({
        userId: user.id,
        email: user.email,
        name: user.name || undefined,
      });

      return {
        success: true,
        data: {
          message: "Account created successfully! Please check your email to verify your account before signing in.",
          user: {
            id: user.id,
            email: user.email,
            roles: user.roles,
            emailVerified: user.emailVerified,
            createdAt: user.createdAt,
          },
          emailSent: emailResult.success,
          emailError: emailResult.error,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Registration failed",
      };
    }
  }

  /**
   * Authenticate user with email and password
   */
  static async loginUser(credentials: LoginCredentials): Promise<AuthServiceResult> {
    try {
      const { email, password } = credentials;

      // Sanitize email
      const sanitizedEmail = sanitizeEmail(email);

      // Find user with password hash
      const user = await prisma.user.findUnique({
        where: { email: sanitizedEmail },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          passwordHash: true,
          roles: true,
          emailVerified: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "Invalid email or password",
        };
      }

      // Check if user has a password (not OAuth-only user)
      if (!user.passwordHash) {
        return {
          success: false,
          error: "This account was created with a social provider. Please sign in using that method.",
        };
      }

      // Verify password
      const isPasswordValid = await verifyPassword(password, user.passwordHash);

      if (!isPasswordValid) {
        return {
          success: false,
          error: "Invalid email or password",
        };
      }

      // Check if email is verified
      if (!user.emailVerified) {
        return {
          success: false,
          error: "EMAIL_NOT_VERIFIED",
          data: {
            userId: user.id,
            email: user.email,
          },
        };
      }

      // Return authenticated user
      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
        roles: user.roles,
        emailVerified: user.emailVerified,
      };

      return {
        success: true,
        data: { user: authUser },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Login failed",
      };
    }
  }

  /**
   * Change user password
   */
  static async changePassword(data: ChangePasswordData): Promise<AuthServiceResult> {
    try {
      const { userId, currentPassword, newPassword } = data;

      // Get user with current password hash
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          passwordHash: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      if (!user.passwordHash) {
        return {
          success: false,
          error: "This account doesn't have a password. It was created with a social provider.",
        };
      }

      // Verify current password
      const isCurrentPasswordValid = await verifyPassword(currentPassword, user.passwordHash);

      if (!isCurrentPasswordValid) {
        return {
          success: false,
          error: "Current password is incorrect",
        };
      }

      // Hash new password
      const newPasswordHash = await hashPassword(newPassword);

      // Update password
      await prisma.user.update({
        where: { id: userId },
        data: { passwordHash: newPasswordHash },
      });

      return {
        success: true,
        data: { message: "Password changed successfully" },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to change password",
      };
    }
  }

  /**
   * Assign roles to user (for onboarding)
   */
  static async assignUserRoles(userId: string, roles: Role[]): Promise<AuthServiceResult> {
    try {
      // Validate roles
      if (!roles || roles.length === 0) {
        return {
          success: false,
          error: "At least one role is required",
        };
      }

      if (roles.length > 2) {
        return {
          success: false,
          error: "Maximum two roles allowed",
        };
      }

      // Use UserService to assign roles
      const result = await UserService.assignRoles(userId, roles);

      if (!result.success) {
        return result;
      }

      return {
        success: true,
        data: {
          message: "Roles assigned successfully",
          user: result.data.user,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to assign roles",
      };
    }
  }

  /**
   * Get user authentication status and info
   */
  static async getUserAuthInfo(userId: string): Promise<AuthServiceResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      return {
        success: true,
        data: {
          user,
          isEmailVerified: !!user.emailVerified,
          hasRoles: user.roles.length > 0,
          needsOnboarding: user.roles.length === 0,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get user auth info",
      };
    }
  }

  /**
   * Determine redirect path based on user roles and verification status
   */
  static getRedirectPath(user: AuthUser): string {
    // If email not verified, redirect to verification page
    if (!user.emailVerified) {
      return "/verify-email";
    }

    // If no roles assigned, redirect to onboarding
    if (user.roles.length === 0) {
      return "/onboarding";
    }

    // If user has only one role, redirect to role-specific dashboard
    if (user.roles.length === 1) {
      const role = user.roles[0];
      switch (role) {
        case Role.MODEL_PROVIDER:
          return "/dashboard/provider";
        case Role.ADVERTISER:
          return "/dashboard/advertiser";
        default:
          return "/dashboard";
      }
    }

    // If user has multiple roles, redirect to role selection dashboard
    return "/dashboard";
  }

  /**
   * Check if user has required role for access
   */
  static async checkUserAccess(userId: string, requiredRole: Role): Promise<AuthServiceResult> {
    try {
      const result = await UserService.hasRole(userId, requiredRole);

      if (!result.success) {
        return result;
      }

      return {
        success: true,
        data: {
          hasAccess: result.data.hasRole,
          message: result.data.hasRole ? "Access granted" : "Access denied",
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to check user access",
      };
    }
  }

  /**
   * Verify email using token
   */
  static async verifyUserEmail(token: string): Promise<AuthServiceResult> {
    try {
      const result = await EmailService.verifyEmail(token);

      if (!result.success) {
        return result;
      }

      return {
        success: true,
        data: {
          message: "Email verified successfully! You can now sign in to your account.",
          user: result.data.user,
          welcomeEmailSent: result.data.welcomeEmailSent,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Email verification failed",
      };
    }
  }

  /**
   * Resend verification email
   */
  static async resendVerificationEmail(email: string): Promise<AuthServiceResult> {
    try {
      const result = await EmailService.resendVerificationEmail({ email });

      if (!result.success) {
        return result;
      }

      return {
        success: true,
        data: {
          message: "Verification email sent successfully! Please check your inbox.",
          emailSent: result.data?.emailSent || false,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to resend verification email",
      };
    }
  }
}

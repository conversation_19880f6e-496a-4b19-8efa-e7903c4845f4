import { AppStatus } from "@prisma/client";
import { nanoid } from "nanoid";
import { prisma } from "@/lib/db";
import { getAppAnalytics } from "@/lib/analytics";

export interface AppServiceResult {
  success: boolean;
  error?: string;
  data?: any;
}

export interface CreateAppData {
  userId: string;
  name: string;
  description?: string;
}

export interface UpdateAppData {
  name?: string;
  description?: string;
  status?: AppStatus;
}

export interface AppWithAnalytics {
  id: string;
  name: string;
  appId: string;
  appSecret?: string;
  description: string | null;
  status: AppStatus;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  impressions: number;
  clicks: number;
  revenue: number;
  ctr: number;
}

export interface AppCredentials {
  appId: string;
  appSecret: string;
}

/**
 * App Service - Business logic layer for application management operations
 * Handles app creation, updates, credential management, and analytics
 */
export class AppService {
  /**
   * Create a new app for a user
   */
  static async createApp(data: CreateAppData): Promise<AppServiceResult> {
    try {
      const { userId, name, description } = data;

      // Validate user exists and has MODEL_PROVIDER role
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, roles: true },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      if (!user.roles.includes("MODEL_PROVIDER")) {
        return {
          success: false,
          error: "User must have MODEL_PROVIDER role to create apps",
        };
      }

      // Generate unique app ID and secret
      const appId = `app_${nanoid(16)}`;
      const appSecret = `secret_${nanoid(32)}`;

      // Create app
      const app = await prisma.app.create({
        data: {
          userId,
          name,
          appId,
          appSecret,
          description: description || null,
          status: AppStatus.ACTIVE,
        },
        select: {
          id: true,
          name: true,
          appId: true,
          appSecret: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return {
        success: true,
        data: {
          message: "App created successfully",
          app: {
            ...app,
            impressions: 0,
            clicks: 0,
            revenue: 0,
            ctr: 0,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create app",
      };
    }
  }

  /**
   * Get app by ID
   */
  static async getAppById(appId: string, includeSecret = false): Promise<AppServiceResult> {
    try {
      const selectFields: any = {
        id: true,
        name: true,
        appId: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      };

      if (includeSecret) {
        selectFields.appSecret = true;
      }

      const app = await prisma.app.findUnique({
        where: { id: appId },
        select: selectFields,
      });

      if (!app) {
        return {
          success: false,
          error: "App not found",
        };
      }

      // Get analytics data
      const analytics = await getAppAnalytics(app.id);

      const appWithAnalytics: AppWithAnalytics = {
        ...app,
        appSecret: includeSecret ? app.appSecret : undefined,
        impressions: analytics.impressions,
        clicks: analytics.clicks,
        revenue: analytics.revenue,
        ctr: analytics.ctr,
      };

      return {
        success: true,
        data: { app: appWithAnalytics },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get app",
      };
    }
  }

  /**
   * Get app by appId (public identifier)
   */
  static async getAppByAppId(appId: string, includeSecret = false): Promise<AppServiceResult> {
    try {
      const selectFields: any = {
        id: true,
        name: true,
        appId: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      };

      if (includeSecret) {
        selectFields.appSecret = true;
      }

      const app = await prisma.app.findUnique({
        where: { appId },
        select: selectFields,
      });

      if (!app) {
        return {
          success: false,
          error: "App not found",
        };
      }

      // Get analytics data
      const analytics = await getAppAnalytics(app.id);

      const appWithAnalytics: AppWithAnalytics = {
        ...app,
        appSecret: includeSecret ? app.appSecret : undefined,
        impressions: analytics.impressions,
        clicks: analytics.clicks,
        revenue: analytics.revenue,
        ctr: analytics.ctr,
      };

      return {
        success: true,
        data: { app: appWithAnalytics },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get app",
      };
    }
  }

  /**
   * Get all apps for a user
   */
  static async getUserApps(userId: string): Promise<AppServiceResult> {
    try {
      const apps = await prisma.app.findMany({
        where: { userId },
        select: {
          id: true,
          name: true,
          appId: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Get analytics for each app
      const appsWithAnalytics = await Promise.all(
        apps.map(async (app) => {
          const analytics = await getAppAnalytics(app.id);
          return {
            ...app,
            impressions: analytics.impressions,
            clicks: analytics.clicks,
            revenue: analytics.revenue,
            ctr: analytics.ctr,
          };
        }),
      );

      return {
        success: true,
        data: { apps: appsWithAnalytics },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get user apps",
      };
    }
  }

  /**
   * Update app information
   */
  static async updateApp(appId: string, data: UpdateAppData, userId?: string): Promise<AppServiceResult> {
    try {
      const { name, description, status } = data;

      // Build where clause
      const whereClause: any = { id: appId };
      if (userId) {
        whereClause.userId = userId; // Ensure user can only update their own apps
      }

      // Prepare update data
      const updateData: any = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (status !== undefined) updateData.status = status;

      const updatedApp = await prisma.app.update({
        where: whereClause,
        data: updateData,
        select: {
          id: true,
          name: true,
          appId: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Get analytics data
      const analytics = await getAppAnalytics(updatedApp.id);

      const appWithAnalytics: AppWithAnalytics = {
        ...updatedApp,
        impressions: analytics.impressions,
        clicks: analytics.clicks,
        revenue: analytics.revenue,
        ctr: analytics.ctr,
      };

      return {
        success: true,
        data: {
          message: "App updated successfully",
          app: appWithAnalytics,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update app",
      };
    }
  }

  /**
   * Delete app
   */
  static async deleteApp(appId: string, userId?: string): Promise<AppServiceResult> {
    try {
      // Build where clause
      const whereClause: any = { id: appId };
      if (userId) {
        whereClause.userId = userId; // Ensure user can only delete their own apps
      }

      await prisma.app.delete({
        where: whereClause,
      });

      return {
        success: true,
        data: { message: "App deleted successfully" },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete app",
      };
    }
  }

  /**
   * Verify app credentials
   */
  static async verifyAppCredentials(appId: string, appSecret: string): Promise<AppServiceResult> {
    try {
      const app = await prisma.app.findUnique({
        where: { appId },
        select: {
          id: true,
          name: true,
          appSecret: true,
          status: true,
          userId: true,
        },
      });

      if (!app) {
        return {
          success: false,
          error: "Invalid app credentials",
        };
      }

      if (app.appSecret !== appSecret) {
        return {
          success: false,
          error: "Invalid app credentials",
        };
      }

      if (app.status !== AppStatus.ACTIVE) {
        return {
          success: false,
          error: "App is not active",
        };
      }

      return {
        success: true,
        data: {
          app: {
            id: app.id,
            name: app.name,
            status: app.status,
            userId: app.userId,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to verify app credentials",
      };
    }
  }

  /**
   * Regenerate app secret
   */
  static async regenerateAppSecret(appId: string, userId?: string): Promise<AppServiceResult> {
    try {
      // Build where clause
      const whereClause: any = { id: appId };
      if (userId) {
        whereClause.userId = userId; // Ensure user can only regenerate their own app secrets
      }

      // Generate new secret
      const newAppSecret = `secret_${nanoid(32)}`;

      const updatedApp = await prisma.app.update({
        where: whereClause,
        data: { appSecret: newAppSecret },
        select: {
          id: true,
          name: true,
          appId: true,
          appSecret: true,
          description: true,
          status: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return {
        success: true,
        data: {
          message: "App secret regenerated successfully",
          app: updatedApp,
          newSecret: newAppSecret,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to regenerate app secret",
      };
    }
  }

  /**
   * Get app analytics
   */
  static async getAppAnalytics(appId: string, userId?: string): Promise<AppServiceResult> {
    try {
      // Verify app exists and user has access
      const whereClause: any = { id: appId };
      if (userId) {
        whereClause.userId = userId;
      }

      const app = await prisma.app.findUnique({
        where: whereClause,
        select: { id: true, name: true },
      });

      if (!app) {
        return {
          success: false,
          error: "App not found or access denied",
        };
      }

      // Get analytics data
      const analytics = await getAppAnalytics(app.id);

      return {
        success: true,
        data: {
          appId: app.id,
          appName: app.name,
          analytics,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get app analytics",
      };
    }
  }

  /**
   * Get app statistics for all apps
   */
  static async getAppStats(): Promise<AppServiceResult> {
    try {
      const [totalApps, activeApps, suspendedApps] = await Promise.all([
        prisma.app.count(),
        prisma.app.count({
          where: { status: AppStatus.ACTIVE },
        }),
        prisma.app.count({
          where: { status: AppStatus.SUSPENDED },
        }),
      ]);

      return {
        success: true,
        data: {
          totalApps,
          activeApps,
          suspendedApps,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get app statistics",
      };
    }
  }
}

import { Role } from "@prisma/client";

import { prisma } from "@/lib/db";
import { hashPassword } from "@/lib/auth";
import { sanitizeEmail } from "@/lib/validation";

export interface UserServiceResult {
  success: boolean;
  error?: string;
  data?: any;
}

export interface CreateUserData {
  email: string;
  password?: string;
  name?: string;
  roles: Role[];
  emailVerified?: boolean;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  roles?: Role[];
  image?: string;
}

export interface UpdateProfileData {
  name?: string;
  image?: string;
}

export interface UserWithRoles {
  id: string;
  email: string;
  name: string | null;
  image: string | null;
  roles: Role[];
  emailVerified: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User Service - Business logic layer for user management operations
 * Handles user creation, updates, role management, and profile operations
 */
export class UserService {
  /**
   * Create a new user
   */
  static async createUser(data: CreateUserData): Promise<UserServiceResult> {
    try {
      const { email, password, name, roles, emailVerified = false } = data;

      // Sanitize email
      const sanitizedEmail = sanitizeEmail(email);

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: sanitizedEmail },
      });

      if (existingUser) {
        return {
          success: false,
          error: "User with this email already exists",
        };
      }

      // Hash password if provided
      let passwordHash: string | undefined;

      if (password) {
        passwordHash = await hashPassword(password);
      }

      // Create user
      const user = await prisma.user.create({
        data: {
          email: sanitizedEmail,
          passwordHash,
          name: name || null,
          roles,
          emailVerified: emailVerified ? new Date() : null,
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return {
        success: true,
        data: { user },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create user",
      };
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<UserServiceResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      return {
        success: true,
        data: { user },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get user",
      };
    }
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string): Promise<UserServiceResult> {
    try {
      const sanitizedEmail = sanitizeEmail(email);

      const user = await prisma.user.findUnique({
        where: { email: sanitizedEmail },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      return {
        success: true,
        data: { user },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get user",
      };
    }
  }

  /**
   * Update user information
   */
  static async updateUser(
    userId: string,
    data: UpdateUserData,
  ): Promise<UserServiceResult> {
    try {
      const { name, email, roles, image } = data;

      // Prepare update data
      const updateData: any = {};

      if (name !== undefined) updateData.name = name;
      if (image !== undefined) updateData.image = image;
      if (roles !== undefined) updateData.roles = roles;

      // Handle email update with sanitization
      if (email !== undefined) {
        const sanitizedEmail = sanitizeEmail(email);

        // Check if new email is already taken by another user
        const existingUser = await prisma.user.findFirst({
          where: {
            email: sanitizedEmail,
            NOT: { id: userId },
          },
        });

        if (existingUser) {
          return {
            success: false,
            error: "Email is already taken by another user",
          };
        }

        updateData.email = sanitizedEmail;
        // Reset email verification if email is changed
        updateData.emailVerified = null;
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return {
        success: true,
        data: { user: updatedUser },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update user",
      };
    }
  }

  /**
   * Update user profile (name and image only)
   */
  static async updateProfile(
    userId: string,
    data: UpdateProfileData,
  ): Promise<UserServiceResult> {
    try {
      const { name, image } = data;

      const updateData: any = {};

      if (name !== undefined) updateData.name = name;
      if (image !== undefined) updateData.image = image;

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return {
        success: true,
        data: { user: updatedUser },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to update profile",
      };
    }
  }

  /**
   * Assign roles to user
   */
  static async assignRoles(
    userId: string,
    roles: Role[],
  ): Promise<UserServiceResult> {
    try {
      // Validate roles
      const validRoles = roles.filter((role) =>
        Object.values(Role).includes(role),
      );

      if (validRoles.length === 0) {
        return {
          success: false,
          error: "At least one valid role is required",
        };
      }

      if (validRoles.length > 2) {
        return {
          success: false,
          error: "Maximum two roles allowed",
        };
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { roles: validRoles },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return {
        success: true,
        data: { user: updatedUser },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to assign roles",
      };
    }
  }

  /**
   * Delete user
   */
  static async deleteUser(userId: string): Promise<UserServiceResult> {
    try {
      await prisma.user.delete({
        where: { id: userId },
      });

      return {
        success: true,
        data: { message: "User deleted successfully" },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete user",
      };
    }
  }

  /**
   * Check if user has specific role
   */
  static async hasRole(userId: string, role: Role): Promise<UserServiceResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { roles: true },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      return {
        success: true,
        data: { hasRole: user.roles.includes(role) },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to check user role",
      };
    }
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(role: Role): Promise<UserServiceResult> {
    try {
      const users = await prisma.user.findMany({
        where: {
          roles: {
            has: role,
          },
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          roles: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return {
        success: true,
        data: { users },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get users by role",
      };
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats(): Promise<UserServiceResult> {
    try {
      const [totalUsers, verifiedUsers, modelProviders, advertisers] =
        await Promise.all([
          prisma.user.count(),
          prisma.user.count({
            where: {
              emailVerified: { not: null },
            },
          }),
          prisma.user.count({
            where: {
              roles: { has: Role.MODEL_PROVIDER },
            },
          }),
          prisma.user.count({
            where: {
              roles: { has: Role.ADVERTISER },
            },
          }),
        ]);

      return {
        success: true,
        data: {
          totalUsers,
          verifiedUsers,
          unverifiedUsers: totalUsers - verifiedUsers,
          modelProviders,
          advertisers,
        },
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get user statistics",
      };
    }
  }
}

import { describe, it, expect, vi, beforeEach } from "vitest";
import { AdStatus, BidType } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";
import { AdService } from "./ad";
import { prisma } from "@/lib/db";
import * as analytics from "@/lib/analytics";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
    },
    advertisement: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
    app: {
      findUnique: vi.fn(),
    },
    adImpression: {
      create: vi.fn(),
    },
  },
}));

vi.mock("@/lib/analytics", () => ({
  getAdAnalytics: vi.fn(),
}));

describe("AdService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(analytics.getAdAnalytics).mockResolvedValue({
      impressions: 100,
      clicks: 10,
      spend: 50.0,
      ctr: 10.0,
    });
  });

  describe("createAd", () => {
    it("should create advertisement successfully", async () => {
      const mockCreateData = {
        userId: "user123",
        name: "Test Ad",
        description: "Test Description",
        productUrl: "https://example.com",
        imageUrl: "https://example.com/image.jpg",
        targetTopics: ["AI", "tech"],
        budget: 1000,
        bidType: BidType.CPC,
        bidAmount: 0.5,
      };

      const mockUser = {
        id: "user123",
        roles: ["ADVERTISER"],
      };

      const mockCreatedAd = {
        id: "ad123",
        name: "Test Ad",
        description: "Test Description",
        imageUrl: "https://example.com/image.jpg",
        productUrl: "https://example.com",
        targetTopics: ["AI", "tech"],
        budget: new Decimal(1000),
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
        status: AdStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(prisma.advertisement.create).mockResolvedValue(mockCreatedAd as any);

      const result = await AdService.createAd(mockCreateData);

      expect(result.success).toBe(true);
      expect(result.data?.ad.name).toBe("Test Ad");
      expect(result.data?.ad.budget).toBe(1000);
      expect(result.data?.ad.bidAmount).toBe(0.5);
    });

    it("should handle user not found", async () => {
      const mockCreateData = {
        userId: "nonexistent",
        name: "Test Ad",
        description: "Test Description",
        productUrl: "https://example.com",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: BidType.CPC,
        bidAmount: 0.5,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await AdService.createAd(mockCreateData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("User not found");
    });

    it("should handle user without ADVERTISER role", async () => {
      const mockCreateData = {
        userId: "user123",
        name: "Test Ad",
        description: "Test Description",
        productUrl: "https://example.com",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: BidType.CPC,
        bidAmount: 0.5,
      };

      const mockUser = {
        id: "user123",
        roles: ["MODEL_PROVIDER"],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await AdService.createAd(mockCreateData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("User must have ADVERTISER role to create ads");
    });
  });

  describe("getAdById", () => {
    it("should get advertisement successfully", async () => {
      const mockAd = {
        id: "ad123",
        name: "Test Ad",
        description: "Test Description",
        imageUrl: "https://example.com/image.jpg",
        productUrl: "https://example.com",
        targetTopics: ["AI", "tech"],
        budget: new Decimal(1000),
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
        status: AdStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(mockAd as any);

      const result = await AdService.getAdById("ad123");

      expect(result.success).toBe(true);
      expect(result.data?.ad.name).toBe("Test Ad");
      expect(result.data?.ad.budget).toBe(1000);
      expect(result.data?.ad.impressions).toBe(100);
    });

    it("should handle advertisement not found", async () => {
      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(null);

      const result = await AdService.getAdById("nonexistent");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Advertisement not found");
    });
  });

  describe("getUserAds", () => {
    it("should get user advertisements successfully", async () => {
      const mockAds = [
        {
          id: "ad1",
          name: "Ad 1",
          description: "Description 1",
          imageUrl: "https://example.com/image1.jpg",
          productUrl: "https://example.com/1",
          targetTopics: ["AI"],
          budget: new Decimal(1000),
          bidType: BidType.CPC,
          bidAmount: new Decimal(0.5),
          status: AdStatus.ACTIVE,
          userId: "user123",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds as any);

      const result = await AdService.getUserAds("user123");

      expect(result.success).toBe(true);
      expect(result.data?.ads).toHaveLength(1);
      expect(result.data?.ads[0].impressions).toBe(100);
    });
  });

  describe("updateAd", () => {
    it("should update advertisement successfully", async () => {
      const mockUpdatedAd = {
        id: "ad123",
        name: "Updated Ad",
        description: "Updated Description",
        imageUrl: "https://example.com/updated.jpg",
        productUrl: "https://example.com/updated",
        targetTopics: ["AI", "updated"],
        budget: new Decimal(2000),
        bidType: BidType.CPM,
        bidAmount: new Decimal(1.0),
        status: AdStatus.ACTIVE,
        userId: "user123",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.advertisement.update).mockResolvedValue(mockUpdatedAd as any);

      const result = await AdService.updateAd("ad123", {
        name: "Updated Ad",
        budget: 2000,
      });

      expect(result.success).toBe(true);
      expect(result.data?.ad.name).toBe("Updated Ad");
      expect(result.data?.ad.budget).toBe(2000);
    });
  });

  describe("serveAd", () => {
    it("should serve advertisement successfully", async () => {
      const mockApp = {
        id: "app123",
        appSecret: "secret123",
        status: "ACTIVE",
        name: "Test App",
      };

      const mockAds = [
        {
          id: "ad1",
          name: "Ad 1",
          description: "Description 1",
          imageUrl: "https://example.com/image1.jpg",
          productUrl: "https://example.com/1",
          targetTopics: ["AI"],
          bidAmount: new Decimal(0.5),
          bidType: BidType.CPC,
        },
      ];

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds as any);

      const result = await AdService.serveAd({
        appId: "app_123",
        appSecret: "secret123",
        topics: ["AI"],
      });

      expect(result.success).toBe(true);
      expect(result.data?.ad.name).toBe("Ad 1");
      expect(result.data?.trackingUrl).toContain("/api/impressions");
    });

    it("should handle invalid app credentials", async () => {
      vi.mocked(prisma.app.findUnique).mockResolvedValue(null);

      const result = await AdService.serveAd({
        appId: "invalid_app",
        appSecret: "invalid_secret",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid credentials or inactive app");
    });

    it("should handle no available ads", async () => {
      const mockApp = {
        id: "app123",
        appSecret: "secret123",
        status: "ACTIVE",
        name: "Test App",
      };

      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue([]);

      const result = await AdService.serveAd({
        appId: "app_123",
        appSecret: "secret123",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("No advertisements available");
    });
  });

  describe("recordImpression", () => {
    it("should record impression successfully", async () => {
      const mockAd = {
        id: "ad123",
        status: AdStatus.ACTIVE,
      };

      const mockApp = {
        id: "app123",
        status: "ACTIVE",
      };

      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(mockAd as any);
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);
      vi.mocked(prisma.adImpression.create).mockResolvedValue({} as any);

      const result = await AdService.recordImpression("ad123", "app123", false);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe("Impression recorded successfully");
    });

    it("should record click successfully", async () => {
      const mockAd = {
        id: "ad123",
        status: AdStatus.ACTIVE,
      };

      const mockApp = {
        id: "app123",
        status: "ACTIVE",
      };

      vi.mocked(prisma.advertisement.findUnique).mockResolvedValue(mockAd as any);
      vi.mocked(prisma.app.findUnique).mockResolvedValue(mockApp as any);
      vi.mocked(prisma.adImpression.create).mockResolvedValue({} as any);

      const result = await AdService.recordImpression("ad123", "app123", true);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe("Click recorded successfully");
    });
  });

  describe("getAdStats", () => {
    it("should get advertisement statistics successfully", async () => {
      vi.mocked(prisma.advertisement.count)
        .mockResolvedValueOnce(100) // total ads
        .mockResolvedValueOnce(80)  // active ads
        .mockResolvedValueOnce(15)  // paused ads
        .mockResolvedValueOnce(5);  // completed ads

      const result = await AdService.getAdStats();

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        totalAds: 100,
        activeAds: 80,
        pausedAds: 15,
        completedAds: 5,
      });
    });
  });
});

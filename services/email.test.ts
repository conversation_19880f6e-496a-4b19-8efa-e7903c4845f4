import { describe, it, expect, vi, beforeEach } from "vitest";
import { EmailService } from "./email";
import { prisma } from "@/lib/db";
import * as emailLib from "@/lib/email";

// Mock the email library
vi.mock("@/lib/email", () => ({
  sendEmail: vi.fn(),
  sendVerificationEmail: vi.fn(),
  sendWelcomeEmail: vi.fn(),
  generateVerificationToken: vi.fn(),
  getVerificationExpiry: vi.fn(),
}));

// Mock Prisma
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      findFirst: vi.fn(),
      update: vi.fn(),
    },
  },
}));

describe("EmailService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("sendGenericEmail", () => {
    it("should send email successfully", async () => {
      const mockEmailData = {
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
      };

      vi.mocked(emailLib.sendEmail).mockResolvedValue({
        success: true,
        provider: "gmail",
      });

      const result = await EmailService.sendGenericEmail(mockEmailData);

      expect(result.success).toBe(true);
      expect(result.data?.provider).toBe("gmail");
      expect(emailLib.sendEmail).toHaveBeenCalledWith(mockEmailData);
    });

    it("should handle email sending failure", async () => {
      const mockEmailData = {
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
      };

      vi.mocked(emailLib.sendEmail).mockResolvedValue({
        success: false,
        error: "Email service unavailable",
      });

      const result = await EmailService.sendGenericEmail(mockEmailData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Email service unavailable");
    });
  });

  describe("createVerificationToken", () => {
    it("should create verification token and send email successfully", async () => {
      const mockData = {
        userId: "user123",
        email: "<EMAIL>",
        name: "Test User",
      };

      const mockToken = "mock-token-123";
      const mockExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);

      vi.mocked(emailLib.generateVerificationToken).mockReturnValue(mockToken);
      vi.mocked(emailLib.getVerificationExpiry).mockReturnValue(mockExpiry);
      vi.mocked(prisma.user.update).mockResolvedValue({
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        emailVerificationToken: mockToken,
        emailVerificationExpires: mockExpiry,
      } as any);
      vi.mocked(emailLib.sendVerificationEmail).mockResolvedValue({
        success: true,
      });

      const result = await EmailService.createVerificationToken(mockData);

      expect(result.success).toBe(true);
      expect(result.data?.token).toBe(mockToken);
      expect(result.data?.emailSent).toBe(true);
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user123" },
        data: {
          emailVerificationToken: mockToken,
          emailVerificationExpires: mockExpiry,
        },
        select: expect.any(Object),
      });
    });

    it("should handle database error", async () => {
      const mockData = {
        userId: "user123",
        email: "<EMAIL>",
        name: "Test User",
      };

      vi.mocked(emailLib.generateVerificationToken).mockReturnValue("mock-token");
      vi.mocked(emailLib.getVerificationExpiry).mockReturnValue(new Date());
      vi.mocked(prisma.user.update).mockRejectedValue(new Error("Database error"));

      const result = await EmailService.createVerificationToken(mockData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Database error");
    });
  });

  describe("verifyEmail", () => {
    it("should verify email successfully", async () => {
      const mockToken = "valid-token";
      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        emailVerified: null,
      };

      const mockUpdatedUser = {
        ...mockUser,
        emailVerified: new Date(),
      };

      vi.mocked(prisma.user.findFirst).mockResolvedValue(mockUser as any);
      vi.mocked(prisma.user.update).mockResolvedValue(mockUpdatedUser as any);
      vi.mocked(emailLib.sendWelcomeEmail).mockResolvedValue({
        success: true,
      });

      const result = await EmailService.verifyEmail(mockToken);

      expect(result.success).toBe(true);
      expect(result.data?.user.emailVerified).toBeTruthy();
      expect(result.data?.welcomeEmailSent).toBe(true);
    });

    it("should handle invalid token", async () => {
      const mockToken = "invalid-token";

      vi.mocked(prisma.user.findFirst).mockResolvedValue(null);

      const result = await EmailService.verifyEmail(mockToken);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid or expired verification token");
    });

    it("should handle already verified email", async () => {
      const mockToken = "valid-token";
      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        emailVerified: new Date(),
      };

      vi.mocked(prisma.user.findFirst).mockResolvedValue(mockUser as any);

      const result = await EmailService.verifyEmail(mockToken);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Email is already verified");
    });
  });

  describe("resendVerificationEmail", () => {
    it("should resend verification email successfully", async () => {
      const mockData = { email: "<EMAIL>" };
      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        emailVerified: null,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      
      // Mock the createVerificationToken method
      const createTokenSpy = vi.spyOn(EmailService, "createVerificationToken");
      createTokenSpy.mockResolvedValue({
        success: true,
        data: { emailSent: true },
      });

      const result = await EmailService.resendVerificationEmail(mockData);

      expect(result.success).toBe(true);
      expect(createTokenSpy).toHaveBeenCalledWith({
        userId: "user123",
        email: "<EMAIL>",
        name: "Test User",
      });

      createTokenSpy.mockRestore();
    });

    it("should handle user not found", async () => {
      const mockData = { email: "<EMAIL>" };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await EmailService.resendVerificationEmail(mockData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("User not found");
    });

    it("should handle already verified email", async () => {
      const mockData = { email: "<EMAIL>" };
      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        emailVerified: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await EmailService.resendVerificationEmail(mockData);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Email is already verified");
    });
  });

  describe("sendWelcomeEmailToUser", () => {
    it("should send welcome email successfully", async () => {
      vi.mocked(emailLib.sendWelcomeEmail).mockResolvedValue({
        success: true,
      });

      const result = await EmailService.sendWelcomeEmailToUser("<EMAIL>", "Test User");

      expect(result.success).toBe(true);
      expect(emailLib.sendWelcomeEmail).toHaveBeenCalledWith("<EMAIL>", "Test User");
    });

    it("should handle welcome email failure", async () => {
      vi.mocked(emailLib.sendWelcomeEmail).mockResolvedValue({
        success: false,
        error: "Email service error",
      });

      const result = await EmailService.sendWelcomeEmailToUser("<EMAIL>");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Email service error");
    });
  });

  describe("isEmailVerified", () => {
    it("should return verification status", async () => {
      const mockUser = {
        emailVerified: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await EmailService.isEmailVerified("user123");

      expect(result.success).toBe(true);
      expect(result.data?.isVerified).toBe(true);
      expect(result.data?.verifiedAt).toBe(mockUser.emailVerified);
    });

    it("should handle user not found", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const result = await EmailService.isEmailVerified("nonexistent");

      expect(result.success).toBe(false);
      expect(result.error).toBe("User not found");
    });
  });

  describe("getVerificationTokenInfo", () => {
    it("should return token info", async () => {
      const mockExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
      const mockUser = {
        emailVerificationToken: "token123",
        emailVerificationExpires: mockExpiry,
        emailVerified: null,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await EmailService.getVerificationTokenInfo("user123");

      expect(result.success).toBe(true);
      expect(result.data?.hasToken).toBe(true);
      expect(result.data?.isExpired).toBe(false);
      expect(result.data?.isVerified).toBe(false);
    });

    it("should detect expired token", async () => {
      const mockExpiry = new Date(Date.now() - 24 * 60 * 60 * 1000); // Past date
      const mockUser = {
        emailVerificationToken: "token123",
        emailVerificationExpires: mockExpiry,
        emailVerified: null,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await EmailService.getVerificationTokenInfo("user123");

      expect(result.success).toBe(true);
      expect(result.data?.isExpired).toBe(true);
    });
  });
});

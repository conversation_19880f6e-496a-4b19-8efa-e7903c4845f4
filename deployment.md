# AI Advertisement Management Platform - Deployment Guide

## Overview
This guide covers deploying the AI Ad Platform MVP to production environments.

## Prerequisites
- Node.js 18+ or Bun runtime
- PostgreSQL database
- Domain name and SSL certificate
- Environment variables configured

## Environment Variables

### Required Production Variables
```bash
# Database
DATABASE_URL="postgresql://username:password@host:port/database?schema=public"

# NextAuth.js
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-super-secure-secret-key-here"

# App Configuration
NEXT_PUBLIC_APP_URL="https://your-domain.com"
NODE_ENV="production"

# Optional: External Services
# REDIS_URL="redis://localhost:6379" # For rate limiting in production
# SENTRY_DSN="your-sentry-dsn" # For error tracking
# SMTP_HOST="smtp.example.com" # For email notifications
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-email-password"
```

## Database Setup

### 1. Create Production Database
```sql
CREATE DATABASE ai_ad_platform;
CREATE USER ai_ad_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE ai_ad_platform TO ai_ad_user;
```

### 2. Run Migrations
```bash
# Generate Prisma client
bunx prisma generate

# Push schema to database
bunx prisma db push

# Optional: Seed with sample data
bunx prisma db seed
```

## Deployment Options

### Option 1: Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Option 2: Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json bun.lockb* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### Option 3: Traditional VPS/Server
```bash
# Clone repository
git clone https://github.com/your-username/ai-ad-platform.git
cd ai-ad-platform

# Install dependencies
bun install

# Build application
bun run build

# Start with PM2
npm install -g pm2
pm2 start ecosystem.config.js
```

## Security Considerations

### 1. Environment Security
- Use strong, unique passwords for database
- Rotate NEXTAUTH_SECRET regularly
- Enable database SSL in production
- Use environment-specific secrets

### 2. Network Security
- Enable HTTPS/SSL certificates
- Configure firewall rules
- Use CDN for static assets
- Implement DDoS protection

### 3. Application Security
- Rate limiting is implemented
- Input validation on all endpoints
- CSRF protection enabled
- Security headers configured

## Monitoring and Logging

### 1. Application Monitoring
```bash
# Install monitoring tools
npm install @sentry/nextjs

# Configure in next.config.js
const { withSentryConfig } = require('@sentry/nextjs')
```

### 2. Database Monitoring
- Monitor connection pool usage
- Set up query performance alerts
- Regular backup schedule
- Monitor disk space

### 3. Performance Monitoring
- Core Web Vitals tracking
- API response time monitoring
- Error rate tracking
- User analytics

## Scaling Considerations

### 1. Database Scaling
- Connection pooling (PgBouncer)
- Read replicas for analytics
- Database indexing optimization
- Query optimization

### 2. Application Scaling
- Horizontal scaling with load balancer
- CDN for static assets
- Redis for session storage
- Microservices architecture for high traffic

### 3. File Storage Scaling
- Move to cloud storage (AWS S3, Cloudinary)
- Implement image optimization
- CDN for image delivery

## Backup Strategy

### 1. Database Backups
```bash
# Daily automated backups
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Restore from backup
psql $DATABASE_URL < backup_20241214.sql
```

### 2. File Backups
- Regular backup of uploaded images
- Version control for code
- Configuration backup

## Health Checks

### 1. Application Health
```javascript
// pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  })
}
```

### 2. Database Health
```javascript
// Check database connectivity
const healthCheck = async () => {
  try {
    await prisma.$queryRaw`SELECT 1`
    return { database: 'healthy' }
  } catch (error) {
    return { database: 'unhealthy', error: error.message }
  }
}
```

## Maintenance

### 1. Regular Updates
- Security patches
- Dependency updates
- Database maintenance
- Log rotation

### 2. Performance Optimization
- Bundle size analysis
- Database query optimization
- Image optimization
- Caching strategies

## Troubleshooting

### Common Issues
1. **Database Connection Errors**
   - Check DATABASE_URL format
   - Verify network connectivity
   - Check connection limits

2. **Authentication Issues**
   - Verify NEXTAUTH_SECRET
   - Check NEXTAUTH_URL configuration
   - Validate session configuration

3. **File Upload Issues**
   - Check file permissions
   - Verify upload directory exists
   - Check file size limits

### Logs Location
- Application logs: `/var/log/ai-ad-platform/`
- Database logs: PostgreSQL log directory
- Web server logs: Nginx/Apache log directory

## Support
For deployment issues, check:
1. Application logs
2. Database connectivity
3. Environment variables
4. Network configuration
5. SSL certificate validity
